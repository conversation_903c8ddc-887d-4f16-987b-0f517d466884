<?php

use App\Http\Controllers\DatenControllerExportSap;
use App\Livewire\FehlerErfassungForm;
use App\Livewire\HistorieComponent;
use Illuminate\Support\Facades\Route;
use App\Http\Middleware\ConfigurationAccessMiddleware;

Route::get('/', function () {
    return view('welcome');
});

Route::middleware([
    'auth:sanctum',
    config('jetstream.auth_session'),
    'verified',
])->group(function () {
    Route::get('/dashboard', [App\Http\Controllers\DashboardController::class, 'index'])
        ->middleware(['auth', 'verified'])
        ->name('dashboard');

    Route::get('/fehler-erfassung', FehlerErfassungForm::class)->name('fehler-erfassung');
    Route::get('/historie', HistorieComponent::class)->name('historie');
    Route::get('/daten-uebersicht', [DatenControllerExportSap::class, 'index'])->name('daten.uebersicht');
    Route::get('/daten-export-csv', [DatenControllerExportSap::class, 'exportCSV'])->name('daten.export.csv');
    Route::get('/daten-export-json', [DatenControllerExportSap::class, 'exportJSON'])->name('daten.export.json');

    // Konfigurationsrouten
    Route::get('/config/password', \App\Livewire\ConfigPasswordComponent::class)->name('config.password');
    Route::get('/konfiguration', \App\Livewire\KonfigurationComponent::class)
        ->middleware(ConfigurationAccessMiddleware::class)
        ->name('konfiguration');

    // Normal-Buchungen Datenübersicht
    Route::get('/daten/normal', \App\Livewire\DatenNormalComponent::class)
        ->name('daten.normal.index');
    Route::get('/daten/normal/export/csv', [App\Http\Controllers\DatenControllerNormal::class, 'exportCSV'])
        ->name('daten.normal.export.csv');
    Route::get('/daten/normal/export/json', [App\Http\Controllers\DatenControllerNormal::class, 'exportJSON'])
        ->name('daten.normal.export.json');
	
    Route::get('/ergebnisbericht/{buchungId}', App\Livewire\ErgebnisberichtComponent::class)->name('ergebnisbericht');
});
