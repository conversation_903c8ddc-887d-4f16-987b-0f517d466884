**Ausschussbuchung Automotiv:**

Erstelle mir eine Formular für die Ausschuss-Erfassung mit Kommentar funktion und Foto und Kammera Upload und Aufnahme funktion die Bilder sollen in der Datenbank gespeichert werden und können später wieder abgerufen werden .
Beim Aufruf der Seite Kommt Hinweis Titel 'Leseband oder Maus?' mit Kurze Beschreibung 'Sind die Teile gestrahlt oder geputzt ⁉️' mit einem Button 'STRAHLEN' und 'PUTZEN'.

hier das Beispiel für das Formular:

```html
<div class="max-w-2xl mx-auto bg-white rounded-lg shadow-lg p-6">
        <!-- Rückmeldenummer -->
        <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-1">
                Rückmeldenummer
            </label>
            <input type="text" 
                   class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
        </div>

        <!-- Modellnest Tabs -->
        <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">
                Modellnest
            </label>
            <div class="grid grid-cols-6 gap-1">
                <button class="bg-blue-500 text-white px-4 py-2 rounded-md">M1</button>
                <button class="bg-gray-200 hover:bg-gray-300 px-4 py-2 rounded-md">M2</button>
                <button class="bg-gray-200 hover:bg-gray-300 px-4 py-2 rounded-md">M3</button>
                <button class="bg-gray-200 hover:bg-gray-300 px-4 py-2 rounded-md">M4</button>
                <button class="bg-gray-200 hover:bg-gray-300 px-4 py-2 rounded-md">M5</button>
                <button class="bg-gray-200 hover:bg-gray-300 px-4 py-2 rounded-md">M6</button>
            </div>
        </div>

        <!-- Fehlermerkmale -->
        <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-1">
                Fehlermerkmal
            </label>
            <select class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                <option value="">Bitte wählen...</option>

            </select>
        </div>

        <!-- Fehlerorte -->
        <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-1">
                Fehlerort
            </label>
            <select class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                <option value="">Bitte wählen...</option>

            </select>
        </div>

        <!-- Zähler -->
        <div class="flex items-center justify-center gap-8 p-6 bg-gray-50 rounded-lg">
            <!-- Minus Button -->
            <button class="w-16 h-16 bg-red-100 hover:bg-red-200 rounded-xl flex items-center justify-center border-2 border-red-200 disabled:bg-gray-100 disabled:border-gray-200">
                <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"/>
                </svg>
            </button>
            
            <!-- Zähleranzeige -->
            <div class="flex flex-col items-center">
                <span class="text-sm font-medium text-gray-500">Anzahl Fehler</span>
                <span class="text-4xl font-bold">0</span>
            </div>
            
            <!-- Plus Button -->
            <button class="w-16 h-16 bg-green-100 hover:bg-green-200 rounded-xl flex items-center justify-center border-2 border-green-200 disabled:bg-gray-100 disabled:border-gray-200">
                <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
                </svg>
            </button>
        </div>

        <div class="mt-6 flex justify-between items-center">
            <!-- Absenden Button (links) -->
            <div>
                <button class="bg-green-500 hover:bg-green-600 text-white px-8 py-3 rounded-lg font-medium shadow-sm flex items-center gap-2">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 5l7 7-7 7M5 5l7 7-7 7"/>
                    </svg>
                    Absenden
                </button>
            </div>
        
            <!-- Speichern Button (rechts) -->
            <div>
                <button class="bg-blue-500 hover:bg-blue-600 text-white px-8 py-3 rounded-lg font-medium shadow-sm flex items-center gap-2">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"/>
                    </svg>
                    Speichern
                </button>
            </div>
        </div>

        <!-- Fehlerliste -->
        <div class="mt-6 p-4 bg-gray-50 rounded-lg max-h-48 overflow-y-auto">
            <h3 class="font-medium mb-2">Erfasste Fehler:</h3>
            <div class="text-sm py-1 border-b border-gray-200">
                <span class="font-semibold">M1:</span> 2x W-ASZ – Zerschlagen (Festlager)
            </div>
            <div class="text-sm py-1">
                <span class="font-semibold">M1:</span> 1x W-GIH1 – Formschülpen (Flansch)
            </div>
        </div>
    </div>
```
**Verhalten bei STRAHLEN:**
Wählt man 'STRAHLEN' wird ein Formular angezeigt mit den Feldern Das Style Positiv:
- Rückmeldenummer
- Modellnest M1 bis M6
- Fehlermerkmal (Dropdown mit den Werten aus der Tabelle 'fehlermerkmal_strahlens' Sortierung Alphabetisch)
- Fehlerort (Dropdown mit den Werten aus der Tabelle fehlerort Sortierung Alphabetisch)
- Menge

**Verhalten bei PUTZEN:**
Wählt man 'PUTZEN' wird ein identisches Formular angezeigt mit den Feldern Das Style Negativ:
- Rückmeldenummer 
- Modellnest M1 bis M6
- Fehlermerkmal (Dropdown mit den Werten aus der Tabelle 'fehlermerkmal_putzens' Sortierung Alphabetisch)
- Fehlerort (Dropdown mit den Werten aus der Tabelle 'fehlerort' Sortierung Alphabetisch)
- Menge

**Verhalten bei Eingabe der Daten und Speichern und Absenden:**
- Sobald die Rückmeldenummer, das Modellnest, das Fehlermerkmal, der Fehlerort und die Menge eingegeben wurden muss man die Daten erst mit dem Button 'Speichern' gespeichert werden danach muss das Feld für die Rückmeldenummer gesperrt werden.

- Die Daten die mit "Speichern" gespeichert wurden werden in der Datenbank in der Tabelle 'vorläufige_ausschussbuchungen' gespeichert (dies musst du noch erstellen), erst nachdem die Daten mit "Absenden" gesendet wurden werden die Daten in die Tabelle 'ausschussbuchungen_sap' übernommen.

- Alle anderen Felder sollen jedoch weiterhin zur verfügung stehen, sodass weitere Daten mit der gleichen Rückmeldenummer erfasst werden können. Erst wenn das Button “Absenden” gedrückt wird, soll das Feld für die Rückmeldenummer wieder freigeschaltet werden.

- Das Absenden Button sendet die Daten Direkt an die Datenbank und die Daten werden in der Tabelle 'ausschussbuchungen_sap' gespeichert. 

- Zusätzlich muss für das Feld der Rückmeldenummer eine Verlaufshistorie der Eingabefeld letzten 10 Einträge gespeichert sein.

**Echtzeit-Anzeige:**
   - Nach dem Speichern werden die Daten in einer tabellarischen Übersicht angezeigt mit dem Titel Erfasste Fehler. Diese Übersicht wird in Echtzeit aktualisiert hier sollten die Daten

**Verlaufsanzeige:**
   - Eine separate Anzeige unter der liste Erfasste Fehler listet die gespeicherten Datensätze aus der Tabelle mit dem Titel Letzte Rückmeldungen diese soll immer die letzten 10 Einträge anzeigen.
   - Zusätzlich soll Direkt über den engen Eingabefeld ein Kommentarfeld eingefügt werden die man aus- und einklappen kann.

**MYSQL verhaltensweisen:**
Alle diese Daten sollen in der Datenbank gespeichert werden unter der Tabelle 'ausschussbuchungen_sap' diese muss noch Erstellt werden.
- fehlermerkmal_strahlens
- fehlermerkmal_putzens
- modellnest (M1-M6 sollen als Modellnest-1 bis Modellnest-6 gespeichert werden)
- fehlerort
- rueckemldenummer

**Foto Upload:**
- Es soll ein Foto Upload Feld erstellt werden das mehrere Fotos gleichzeitig hochladen kann.
- Es soll eine Vorschau der hochgeladenen Fotos angezeigt werden.
- Es soll ein Button zum Entfernen eines Fotos angezeigt werden.
- Es soll eine Fehlermeldung angezeigt werden wenn das Foto größer als 10MB ist.

**Hinweis wenn Felder leer sind:**
1. Es soll eine Hinweis geben wenn die Felder leer sind.
2. Nach dem Absenden des Aktuellem Rückmeldenummer Sofort unter Letzte Nummer Anzeigen das sollte möglich sein.
3. Eine reset icon ganz oben recht isn die Ecke als Icon

**Update der Datenbank:**
- wenn Datensätze mit der identischen Rückmeldenummer bereits existieren, sollen die Daten zu dieser Rückmeldenummer im datenbank Tabelle 'ausschussbuchungen_sap' aktualisiert werden nur die Mengen zu den entsprechenden bereits vorhandenen Datensätzen werden mengen erhöht.
- wenn Datensätze mit der identischen Rückmeldenummer nicht existieren, sollen die Daten zu dieser Rückmeldenummer im datenbank Tabelle 'ausschussbuchungen_sap' erstellt werden.

**Erstellung eines Extra Verlaufseite für die Erfassten Daten aus der Tabelle 'ausschussbuchungen_sap' mit den Feldern:**
Es soll eine Verlaufseite erstellt werden die die Tabelle 'ausschussbuchungen_sap' anzeigt mit den gesamten Datenfeldern übersichtlich mit filterfunktionen die man eine und Ausklappen kann sollte ansprechend und Modernes Design haben.
- Rückmeldenummer
- Modellnest
- Fehlermerkmal
- Fehlerort
- Menge
- Kommentar
- Foto

**Verlinkung des Dashboard auf die Formular und Verlaufseite:**
Die Links vom Formular und der Verlaufseite sollen im Dashboard und Navigationsmenü angezeigt werden.

**Erstellung der Datenbank Tabellen fehlermerkmal_strahlens und fehlermerkmal_putzens:**
Für das Feld Fehlermerkmal auf der ansicht 'STRAHLEN' soll eine neue Tabelle in der Datenbank erstellt werden. Dieses Feld wird anschließend automatisch mit den Daten aus dieser Tabelle gefüllt. Hier sind die erforderlichen Daten für MySQL:

```sql
-- phpMyAdmin SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: localhost
-- Erstellungszeit: 30. Dez 2024 um 07:36
-- Server-Version: 9.0.1
-- PHP-Version: 8.4.2

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Datenbank: `qm_abteilung_1`
--

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `fehlermerkmal_strahlens`
--

CREATE TABLE `fehlermerkmal_strahlens` (
  `id` bigint UNSIGNED NOT NULL,
  `code` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `abkuerzung` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `beschreibung` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Daten für Tabelle `fehlermerkmal_strahlens`
--

INSERT INTO `fehlermerkmal_strahlens` (`id`, `code`, `abkuerzung`, `beschreibung`, `created_at`, `updated_at`, `deleted_at`) VALUES
(1, '6305', 'W-SAB', 'Ballenabriss', '2024-12-29 06:36:29', '2024-12-29 06:36:29', NULL),
(2, '6305', 'W-GIJ', 'Sandstellen', '2024-12-29 06:36:29', '2024-12-29 06:36:29', NULL),
(3, '6305', 'W-ASZ', 'Zerschlagen', '2024-12-29 06:36:29', '2024-12-29 06:36:29', NULL),
(4, '6305', 'W-GID', 'Abguß unvollständig', '2024-12-29 06:36:29', '2024-12-29 06:36:29', NULL),
(5, '6305', 'W-SAA1', 'Angebrannter Formsand', '2024-12-29 06:36:29', '2024-12-29 06:36:29', NULL),
(6, '6305', 'W-GIM', 'Kaltschweißstellen', '2024-12-29 06:36:29', '2024-12-29 06:36:29', NULL),
(7, '6305', 'W-GIH1', 'Formschülpen', '2024-12-29 06:36:29', '2024-12-29 06:36:29', NULL),
(8, '6305', 'W-GIN2', 'Kleiner Kern vergessen', '2024-12-29 06:36:29', '2024-12-29 06:36:29', NULL),
(9, '6305', 'W-GIXA', 'Pinholes', '2024-12-29 06:36:29', '2024-12-29 06:36:29', NULL),
(10, '6305', 'W-GIL', 'Schlackestellen', '2024-12-29 06:36:29', '2024-12-29 06:36:29', NULL),
(11, '6305', 'W-GIH2', 'Kernschülpen', '2024-12-29 06:36:29', '2024-12-29 06:36:29', NULL),
(12, '6305', 'W-GIN3', 'Kernfehler', '2024-12-29 06:36:29', '2024-12-29 06:36:29', NULL),
(13, '6305', 'W-SAA2', 'Angebrannter Kernsand', '2024-12-29 06:36:29', '2024-12-29 06:36:29', NULL),
(14, '6305', 'W-SAL', 'Blattrippen', '2024-12-29 06:36:29', '2024-12-29 06:36:29', NULL),
(15, '6305', 'W-ASZ1', 'Speiser ausgebrochen', '2024-12-29 06:36:29', '2024-12-29 06:36:29', NULL),
(16, '6305', 'W-MGC', 'Graphitausbildung', '2024-12-29 06:36:29', '2024-12-29 06:36:29', NULL),
(17, '6305', 'W-GIIL', 'Sandstellen, Läufer', '2024-12-29 06:36:29', '2024-12-29 06:36:29', NULL),
(18, '6305', 'W-GLIIP', 'Sandstellen, punktuell', '2024-12-29 06:36:29', '2024-12-29 06:36:29', NULL);

--
-- Indizes der exportierten Tabellen
--

--
-- Indizes für die Tabelle `fehlermerkmal_strahlens`
--
ALTER TABLE `fehlermerkmal_strahlens`
  ADD PRIMARY KEY (`id`);

--
-- AUTO_INCREMENT für exportierte Tabellen
--

--
-- AUTO_INCREMENT für Tabelle `fehlermerkmal_strahlens`
--
ALTER TABLE `fehlermerkmal_strahlens`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=19;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;


```

Für das Feld Fehlermerkmal auf der ansicht 'Putzen' soll eine neue Tabelle in der Datenbank erstellt werden. Dieses Feld wird anschließend automatisch mit den Daten aus dieser Tabelle gefüllt. Hier sind die erforderlichen Daten für MySQL:

```sql


-- phpMyAdmin SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: localhost
-- Erstellungszeit: 30. Dez 2024 um 07:37
-- Server-Version: 9.0.1
-- PHP-Version: 8.4.2

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Datenbank: `qm_abteilung_1`
--

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `fehlermerkmal_putzens`
--

CREATE TABLE `fehlermerkmal_putzens` (
  `id` bigint UNSIGNED NOT NULL,
  `code` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `abkuerzung` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `beschreibung` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Daten für Tabelle `fehlermerkmal_putzens`
--

INSERT INTO `fehlermerkmal_putzens` (`id`, `code`, `abkuerzung`, `beschreibung`, `created_at`, `updated_at`, `deleted_at`) VALUES
(1, '6306', 'W-SAB', 'Ballenabriss', '2024-12-29 06:36:29', '2024-12-29 06:36:29', NULL),
(2, '6306', 'W-GIJ', 'Sandstellen', '2024-12-29 06:36:29', '2024-12-29 06:36:29', NULL),
(3, '6306', 'W-ASZ', 'Zerschlagen', '2024-12-29 06:36:29', '2024-12-29 06:36:29', NULL),
(4, '6306', 'W-GID', 'Abguß unvollständig', '2024-12-29 06:36:29', '2024-12-29 06:36:29', NULL),
(5, '6306', 'W-SAA1', 'Angebrannter Formsand', '2024-12-29 06:36:29', '2024-12-29 06:36:29', NULL),
(6, '6306', 'W-GIM', 'Kaltschweißstellen', '2024-12-29 06:36:29', '2024-12-29 06:36:29', NULL),
(7, '6306', 'W-GIH1', 'Formschülpen', '2024-12-29 06:36:29', '2024-12-29 06:36:29', NULL),
(8, '6306', 'W-GIN2', 'Kleiner Kern vergessen', '2024-12-29 06:36:29', '2024-12-29 06:36:29', NULL),
(9, '6306', 'W-GIXA', 'Pinholes', '2024-12-29 06:36:29', '2024-12-29 06:36:29', NULL),
(10, '6306', 'W-GIL', 'Schlackestellen', '2024-12-29 06:36:29', '2024-12-29 06:36:29', NULL),
(11, '6306', 'W-GIH2', 'Kernschülpen', '2024-12-29 06:36:29', '2024-12-29 06:36:29', NULL),
(12, '6306', 'W-GIN3', 'Kernfehler', '2024-12-29 06:36:29', '2024-12-29 06:36:29', NULL),
(13, '6306', 'W-SAA2', 'Angebrannter Kernsand', '2024-12-29 06:36:29', '2024-12-29 06:36:29', NULL),
(14, '6306', 'W-SAL', 'Blattrippen', '2024-12-29 06:36:29', '2024-12-29 06:36:29', NULL),
(15, '6306', 'W-ASZ1', 'Speiser ausgebrochen', '2024-12-29 06:36:29', '2024-12-29 06:36:29', NULL),
(16, '6306', 'W-MGC', 'Graphitausbildung', '2024-12-29 06:36:29', '2024-12-29 06:36:29', NULL),
(17, '6306', 'W-GIIL', 'Sandstellen, Läufer', '2024-12-29 06:36:29', '2024-12-29 06:36:29', NULL),
(18, '6306', 'W-GLIIP', 'Sandstellen, punktuell', '2024-12-29 06:36:29', '2024-12-29 06:36:29', NULL);

--
-- Indizes der exportierten Tabellen
--

--
-- Indizes für die Tabelle `fehlermerkmal_putzens`
--
ALTER TABLE `fehlermerkmal_putzens`
  ADD PRIMARY KEY (`id`);

--
-- AUTO_INCREMENT für exportierte Tabellen
--

--
-- AUTO_INCREMENT für Tabelle `fehlermerkmal_putzens`
--
ALTER TABLE `fehlermerkmal_putzens`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=19;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;

```

Für das Feld Fehlerort soll eine neue Tabelle in der Datenbank erstellt werden. Dieses Feld wird anschließend automatisch mit den Daten aus dieser Tabelle gefüllt. Hier sind die erforderlichen Daten für MySQL:

```sql
-- phpMyAdmin SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: localhost
-- Erstellungszeit: 30. Dez 2024 um 07:45
-- Server-Version: 9.0.1
-- PHP-Version: 8.4.2

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Datenbank: `qm_abteilung_1`
--

-- --------------------------------------------------------

--
-- Tabellenstruktur für Tabelle `fehlerort`
--

CREATE TABLE `fehlerort` (
  `id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Daten für Tabelle `fehlerort`
--

INSERT INTO `fehlerort` (`id`, `name`, `created_at`, `updated_at`) VALUES
(1, 'Festlager', '2024-12-29 06:36:29', '2024-12-29 06:36:29'),
(2, 'Flansch', '2024-12-29 06:36:29', '2024-12-29 06:36:29'),
(3, 'Gehäuseboden', '2024-12-29 06:36:29', '2024-12-29 06:36:29'),
(4, 'Kernbereich', '2024-12-29 06:36:29', '2024-12-29 06:36:29'),
(5, 'Kleiner Kern vergessen', '2024-12-29 06:36:29', '2024-12-29 06:36:29'),
(6, 'Loslager', '2024-12-29 06:36:29', '2024-12-29 06:36:29'),
(7, 'Putzen', '2024-12-29 06:36:29', '2024-12-29 06:36:29'),
(8, 'Restliches Bauteil', '2024-12-29 06:36:29', '2024-12-29 06:36:29'),
(9, 'Sattelrücken', '2024-12-29 06:36:29', '2024-12-29 06:36:29'),
(10, 'Trennen', '2024-12-29 06:36:29', '2024-12-29 06:36:29'),
(11, 'Zugstrebe', '2024-12-29 06:36:29', '2024-12-29 06:36:29'),
(12, 'Zugstrebe (Läufer)', '2024-12-29 06:36:29', '2024-12-29 06:36:29'),
(13, 'Zugstrebe (punktuell)', '2024-12-29 06:36:29', '2024-12-29 06:36:29');

--
-- Indizes der exportierten Tabellen
--

--
-- Indizes für die Tabelle `fehlerort`
--
ALTER TABLE `fehlerort`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `fehlerort_name_unique` (`name`);

--
-- AUTO_INCREMENT für exportierte Tabellen
--

--
-- AUTO_INCREMENT für Tabelle `fehlerort`
--
ALTER TABLE `fehlerort`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=14;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;

```



# Cronjob für das Löschen der temporären Bild Dateien:

cd /var/www/vhosts/calhan.de/httpdocs && /opt/plesk/php/8.3/bin/php artisan livewire:cleanup-tmp >/dev/null 2>&1