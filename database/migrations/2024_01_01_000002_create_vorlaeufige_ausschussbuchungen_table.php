<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('vorlaeufige_ausschussbuchungen', function (Blueprint $table) {
            $table->id();
            $table->string('rueckmeldenummer');
            $table->string('modellnest')->nullable();
            $table->string('fehlermerkmal_type');
            $table->unsignedBigInteger('fehlermerkmal_id');
            $table->index(['fehlermerkmal_type', 'fehlermerkmal_id'], 'fehler_morph_idx');
            $table->foreignId('fehlerort_id')->constrained('fehlerorts');
            $table->integer('menge');
            $table->text('kommentar')->nullable();
            $table->json('foto_path')->nullable();
            $table->string('type');
            // Neue Felder für NORMAL-Maske
            $table->string('vorgang')->nullable();
            $table->foreignId('kostenstelle_id')->nullable()->constrained('kostenstellen')->nullOnDelete();
            $table->string('ursache')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('vorlaeufige_ausschussbuchungen');
    }
};
