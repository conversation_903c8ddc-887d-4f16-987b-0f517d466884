<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('ursachen', function (Blueprint $table) {
            $table->id();
            $table->string('kategorie');
            $table->integer('code')->unique();
            $table->string('beschreibung');
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('ursachen');
    }
};
