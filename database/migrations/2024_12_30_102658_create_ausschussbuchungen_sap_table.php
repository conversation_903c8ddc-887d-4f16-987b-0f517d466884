<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ausschussbuchungen_sap', function (Blueprint $table) {
            $table->id();
            $table->string('rueckmeldenummer');
            $table->string('modellnest');
            $table->foreignId('fehlermerkmal_id');
            $table->string('fehlermerkmal_type'); // For polymorphic relationship (strahlen or putzen)
            $table->foreignId('fehlerort_id')->constrained('fehlerorts');
            $table->integer('menge');
            $table->text('kommentar')->nullable();
            $table->json('foto_path')->nullable();
            $table->string('type')->default('strahlen'); // 'strahlen', 'putzen' or 'normal'
            // Zusätzliche Felder für normale Buchungen
            $table->string('vorgang')->nullable();
            $table->foreignId('kostenstelle_id')->nullable()->constrained('kostenstellen')->nullOnDelete();
            $table->string('ursache')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ausschussbuchungen_sap');
    }
};
