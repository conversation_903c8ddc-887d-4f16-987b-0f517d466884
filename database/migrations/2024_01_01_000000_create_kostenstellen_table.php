<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('kostenstellen', function (Blueprint $table) {
            $table->id();
            $table->string('kostenstelle')->unique();
            $table->string('bezeichnung');
            $table->string('hierarchie_bereich');
            $table->string('verantwortlicher');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down()
    {
        Schema::dropIfExists('kostenstellen');
    }
};
