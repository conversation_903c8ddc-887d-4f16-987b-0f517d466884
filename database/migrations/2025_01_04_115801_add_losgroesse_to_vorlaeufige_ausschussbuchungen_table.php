<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('vorlaeufige_ausschussbuchungen', function (Blueprint $table) {
            $table->integer('losgroesse')->nullable()->after('rueckmeldenummer');
        });

        Schema::table('ausschussbuchungen_sap', function (Blueprint $table) {
            $table->integer('losgroesse')->nullable()->after('rueckmeldenummer');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('vorlaeufige_ausschussbuchungen', function (Blueprint $table) {
            $table->dropColumn('losgroesse');
        });

        Schema::table('ausschussbuchungen_sap', function (Blueprint $table) {
            $table->dropColumn('losgroesse');
        });
    }
};
