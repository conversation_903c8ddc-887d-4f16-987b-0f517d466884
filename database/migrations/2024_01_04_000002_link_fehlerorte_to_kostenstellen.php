<?php

use App\Models\Fehlerort;
use App\Models\Kostenstelle;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up()
    {
        // Mapping von Fehlerorten zu Kostenstellen
        $mapping = [
            'Festlager' => '7300',          // KG-Formanlage
            'Flansch' => '7201',            // GRG-Formanlage
            'Gehäuseboden' => '7202',       // GRG-Gieß-Strecke/Formenpuffer
            'Kernbereich' => '7011',        // Sandaufbereitung Cold Box-Kerne
            'Hebeldom' => '7017',           // Schlichten/Nachbehandeln/Bohren Cold Box
            'Loslager' => '7300',           // KG-Formanlage
            'Restliches Bauteil' => '7201', // GRG-Formanlage
            'Sattelrücken' => '7202',       // GRG-Gieß-Strecke/Formenpuffer
            'Zugstrebe' => '7201',          // GRG-Formanlage
        ];

        foreach ($mapping as $fehlerortName => $kostenstelleNummer) {
            $fehlerort = Fehlerort::where('name', $fehlerortName)->first();
            $kostenstelle = Kostenstelle::where('kostenstelle', $kostenstelleNummer)->first();

            if ($fehlerort && $kostenstelle) {
                $fehlerort->update(['kostenstelle_id' => $kostenstelle->id]);
            }
        }
    }

    public function down()
    {
        // Setze alle kostenstelle_id Werte auf null zurück
        Fehlerort::query()->update(['kostenstelle_id' => null]);
    }
};
