<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('fehlermerkmal_strahlens', function (Blueprint $table) {
            $table->id();
            $table->string('code');
            $table->string('abkuerzung');
            $table->string('sap_code');
            $table->string('beschreibung');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('fehlermerkmal_strahlens');
    }
};
