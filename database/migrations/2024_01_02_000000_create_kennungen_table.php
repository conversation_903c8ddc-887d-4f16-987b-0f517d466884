<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('zuordnungen', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('zuordnungmodellnest');
            $table->unsignedInteger('zuordnunglage');
            $table->timestamps();
        });

        // Füge Check Constraints hinzu
        DB::statement('ALTER TABLE zuordnungen ADD CONSTRAINT check_zuordnungmodellnest CHECK (zuordnungmodellnest > 0)');
        DB::statement('ALTER TABLE zuordnungen ADD CONSTRAINT check_zuordnunglage CHECK (zuordnunglage > 0)');
    }

    public function down()
    {
        Schema::dropIfExists('zuordnungen');
    }
};
