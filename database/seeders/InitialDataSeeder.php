<?php

namespace Database\Seeders;

use App\Models\FehlermermalPutzen;
use App\Models\FehlermermalStrahlen;
use App\Models\Fehlerort;
use App\Models\Ursache;
use App\Models\Zuordnungen;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class InitialDataSeeder extends Seeder
{
    public function run(): void
    {
        // Zuordnungen
        $zuordnungen = [
            ['zuordnungmodellnest' => 1000, 'zuordnunglage' => 2000],
        ];

        foreach ($zuordnungen as $zuordnung) {
            Zuordnungen::create($zuordnung);
        }

        // Fehlermerkmale für Normal
        DB::table('fehlermerkmal_normals')->insert([
            ['code' => '6307', 'abkuerzung' => 'W-ABO', 'sap_code' => '20', 'beschreibung' => 'Wanddicke', 'created_at' => now(), 'updated_at' => now()],
            ['code' => '6307', 'abkuerzung' => 'W-ASC', 'sap_code' => '50', 'beschreibung' => 'Korrosion, Rost', 'created_at' => now(), 'updated_at' => now()],
            ['code' => '6307', 'abkuerzung' => 'W-ASM', 'sap_code' => '70', 'beschreibung' => 'falsche Sachnummer geliefert', 'created_at' => now(), 'updated_at' => now()],
            ['code' => '6307', 'abkuerzung' => 'W-ASZ', 'sap_code' => '90', 'beschreibung' => 'Zerschlagen', 'created_at' => now(), 'updated_at' => now()],
            ['code' => '6307', 'abkuerzung' => 'W-FLB', 'sap_code' => '110', 'beschreibung' => 'Ebenheit', 'created_at' => now(), 'updated_at' => now()],
            ['code' => '6307', 'abkuerzung' => 'W-GIAA', 'sap_code' => '130', 'beschreibung' => 'Mikroporosität', 'created_at' => now(), 'updated_at' => now()],
            ['code' => '6307', 'abkuerzung' => 'W-GIB', 'sap_code' => '140', 'beschreibung' => 'Lunker', 'created_at' => now(), 'updated_at' => now()],
            ['code' => '6307', 'abkuerzung' => 'W-GIBA', 'sap_code' => '150', 'beschreibung' => 'Blaslunker', 'created_at' => now(), 'updated_at' => now()],
            ['code' => '6307', 'abkuerzung' => 'W-GIBB', 'sap_code' => '160', 'beschreibung' => 'Aussenlunker / Einfallstelle', 'created_at' => now(), 'updated_at' => now()],
            ['code' => '6307', 'abkuerzung' => 'W-GID', 'sap_code' => '170', 'beschreibung' => 'Abguss unvollständig', 'created_at' => now(), 'updated_at' => now()],
            ['code' => '6307', 'abkuerzung' => 'W-GIF', 'sap_code' => '190', 'beschreibung' => 'Versatz', 'created_at' => now(), 'updated_at' => now()],
            ['code' => '6307', 'abkuerzung' => 'W-GIH', 'sap_code' => '200', 'beschreibung' => 'Schülpen', 'created_at' => now(), 'updated_at' => now()],
            ['code' => '6307', 'abkuerzung' => 'W-GIJ', 'sap_code' => '210', 'beschreibung' => 'Sandstelle', 'created_at' => now(), 'updated_at' => now()],
            ['code' => '6307', 'abkuerzung' => 'W-GIK', 'sap_code' => '220', 'beschreibung' => 'raue Oberfläche', 'created_at' => now(), 'updated_at' => now()],
            ['code' => '6307', 'abkuerzung' => 'W-GIL', 'sap_code' => '230', 'beschreibung' => 'Schlackestellen', 'created_at' => now(), 'updated_at' => now()],
            ['code' => '6307', 'abkuerzung' => 'W-GIM', 'sap_code' => '240', 'beschreibung' => 'Kaltschweissstellen', 'created_at' => now(), 'updated_at' => now()],
            ['code' => '6307', 'abkuerzung' => 'W-GIN', 'sap_code' => '250', 'beschreibung' => 'unmaßlicher Abguss', 'created_at' => now(), 'updated_at' => now()],
            ['code' => '6307', 'abkuerzung' => 'W-GIX', 'sap_code' => '280', 'beschreibung' => 'Gasblasen', 'created_at' => now(), 'updated_at' => now()],
            ['code' => '6307', 'abkuerzung' => 'W-GIXA', 'sap_code' => '290', 'beschreibung' => 'Pinholes', 'created_at' => now(), 'updated_at' => now()],
            ['code' => '6307', 'abkuerzung' => 'W-GIY', 'sap_code' => '300', 'beschreibung' => 'Schaumstellen', 'created_at' => now(), 'updated_at' => now()],
            ['code' => '6307', 'abkuerzung' => 'W-MAF', 'sap_code' => '340', 'beschreibung' => 'Härte', 'created_at' => now(), 'updated_at' => now()],
            ['code' => '6307', 'abkuerzung' => 'W-MAX', 'sap_code' => '350', 'beschreibung' => 'Risse', 'created_at' => now(), 'updated_at' => now()],
            ['code' => '6307', 'abkuerzung' => 'W-MGB', 'sap_code' => '360', 'beschreibung' => 'Einschlüsse', 'created_at' => now(), 'updated_at' => now()],
            ['code' => '6307', 'abkuerzung' => 'W-MGC', 'sap_code' => '370', 'beschreibung' => 'Graphitausbildung', 'created_at' => now(), 'updated_at' => now()],
            ['code' => '6307', 'abkuerzung' => 'W-OBM', 'sap_code' => '420', 'beschreibung' => 'Verputzen unsauber', 'created_at' => now(), 'updated_at' => now()],
            ['code' => '6307', 'abkuerzung' => 'W-OSLA', 'sap_code' => '430', 'beschreibung' => 'Grundierung', 'created_at' => now(), 'updated_at' => now()],
            ['code' => '6307', 'abkuerzung' => 'W-SAA', 'sap_code' => '440', 'beschreibung' => 'angebrannter Sand', 'created_at' => now(), 'updated_at' => now()],
            ['code' => '6307', 'abkuerzung' => 'W-SAB', 'sap_code' => '450', 'beschreibung' => 'Ballenabriss', 'created_at' => now(), 'updated_at' => now()],
            ['code' => '6307', 'abkuerzung' => 'W-SAE', 'sap_code' => '460', 'beschreibung' => 'Explosionspenetration', 'created_at' => now(), 'updated_at' => now()],
            ['code' => '6307', 'abkuerzung' => 'W-SAK', 'sap_code' => '480', 'beschreibung' => 'Kernbruch', 'created_at' => now(), 'updated_at' => now()],
            ['code' => '6307', 'abkuerzung' => 'W-SAL', 'sap_code' => '490', 'beschreibung' => 'Blattrippen', 'created_at' => now(), 'updated_at' => now()],
            ['code' => '6307', 'abkuerzung' => 'W-SAN', 'sap_code' => '500', 'beschreibung' => 'Sandeinschlüsse', 'created_at' => now(), 'updated_at' => now()],
            ['code' => '6307', 'abkuerzung' => 'W-SAR', 'sap_code' => '510', 'beschreibung' => 'freier Sand / Rieselsand', 'created_at' => now(), 'updated_at' => now()],
        ]);

        // Fehlermerkmale für Putzen
        $fehlermerkmalePutzen = [
            ['code' => '6306', 'abkuerzung' => 'W-SAB', 'sap_code' => '0010', 'beschreibung' => 'Ballenabriss'],
            ['code' => '6306', 'abkuerzung' => 'W-GIJ', 'sap_code' => '0020', 'beschreibung' => 'Sandstellen'],
            ['code' => '6306', 'abkuerzung' => 'W-ASZ', 'sap_code' => '0030', 'beschreibung' => 'Zerschlagen'],
            ['code' => '6306', 'abkuerzung' => 'W-GID', 'sap_code' => '0040', 'beschreibung' => 'Abguß unvollständig'],
            ['code' => '6306', 'abkuerzung' => 'W-SAA1', 'sap_code' => '0050', 'beschreibung' => 'Angebrannter Formsand'],
            ['code' => '6306', 'abkuerzung' => 'W-GIM', 'sap_code' => '0060', 'beschreibung' => 'Kaltschweißstellen'],
            ['code' => '6306', 'abkuerzung' => 'W-GIH1', 'sap_code' => '0070', 'beschreibung' => 'Formschülpen'],
            ['code' => '6306', 'abkuerzung' => 'W-GIN2', 'sap_code' => '0080', 'beschreibung' => 'Kleiner Kern vergessen'],
            ['code' => '6306', 'abkuerzung' => 'W-GIXA', 'sap_code' => '0090', 'beschreibung' => 'Pinholes'],
            ['code' => '6306', 'abkuerzung' => 'W-GIL', 'sap_code' => '0100', 'beschreibung' => 'Schlackestellen'],
            ['code' => '6306', 'abkuerzung' => 'W-GIH2', 'sap_code' => '0120', 'beschreibung' => 'Kernschülpen'],
            ['code' => '6306', 'abkuerzung' => 'W-GIN3', 'sap_code' => '0130', 'beschreibung' => 'Kernfehler'],
            ['code' => '6306', 'abkuerzung' => 'W-SAA2', 'sap_code' => '0140', 'beschreibung' => 'Angebrannter Kernsand'],
            ['code' => '6306', 'abkuerzung' => 'W-SAL', 'sap_code' => '0150', 'beschreibung' => 'Blattrippen'],
            ['code' => '6306', 'abkuerzung' => 'W-ASZ1', 'sap_code' => '0180', 'beschreibung' => 'Speiser ausgebrochen'],
            ['code' => '6306', 'abkuerzung' => 'W-MGC', 'sap_code' => '0370', 'beschreibung' => 'Graphitausbildung'],
            ['code' => '6306', 'abkuerzung' => 'W-GIJL', 'sap_code' => '0280', 'beschreibung' => 'Sandstellen, Läufer'],
            ['code' => '6306', 'abkuerzung' => 'W-GIJP', 'sap_code' => '0290', 'beschreibung' => 'Sandstellen, punktuell'],
            ['code' => '6306', 'abkuerzung' => 'W-APR', 'sap_code' => '0500', 'beschreibung' => 'Prüfung'],
        ];

        foreach ($fehlermerkmalePutzen as $merkmal) {
            FehlermermalPutzen::create($merkmal);
        }

        // Fehlermerkmale für Strahlen
        $fehlermerkmalStrahlen = [
            ['code' => '6305', 'abkuerzung' => 'W-SAB', 'sap_code' => '0010', 'beschreibung' => 'Ballenabriss'],
            ['code' => '6305', 'abkuerzung' => 'W-GIJ', 'sap_code' => '0020', 'beschreibung' => 'Sandstellen'],
            ['code' => '6305', 'abkuerzung' => 'W-ASZ', 'sap_code' => '0030', 'beschreibung' => 'Zerschlagen'],
            ['code' => '6305', 'abkuerzung' => 'W-GID', 'sap_code' => '0040', 'beschreibung' => 'Abguß unvollständig'],
            ['code' => '6305', 'abkuerzung' => 'W-SAA1', 'sap_code' => '0050', 'beschreibung' => 'Angebrannter Formsand'],
            ['code' => '6305', 'abkuerzung' => 'W-GIM', 'sap_code' => '0060', 'beschreibung' => 'Kaltschweißstellen'],
            ['code' => '6305', 'abkuerzung' => 'W-GIH1', 'sap_code' => '0070', 'beschreibung' => 'Formschülpen'],
            ['code' => '6305', 'abkuerzung' => 'W-GIN2', 'sap_code' => '0080', 'beschreibung' => 'Kleiner Kern vergessen'],
            ['code' => '6305', 'abkuerzung' => 'W-GIXA', 'sap_code' => '0090', 'beschreibung' => 'Pinholes'],
            ['code' => '6305', 'abkuerzung' => 'W-GIL', 'sap_code' => '0100', 'beschreibung' => 'Schlackestellen'],
            ['code' => '6305', 'abkuerzung' => 'W-GIH2', 'sap_code' => '0120', 'beschreibung' => 'Kernschülpen'],
            ['code' => '6305', 'abkuerzung' => 'W-GIN3', 'sap_code' => '0130', 'beschreibung' => 'Kernfehler'],
            ['code' => '6305', 'abkuerzung' => 'W-SAA2', 'sap_code' => '0140', 'beschreibung' => 'Angebrannter Kernsand'],
            ['code' => '6305', 'abkuerzung' => 'W-SAL', 'sap_code' => '0150', 'beschreibung' => 'Blattrippen'],
            ['code' => '6305', 'abkuerzung' => 'W-ASZ1', 'sap_code' => '0180', 'beschreibung' => 'Speiser ausgebrochen'],
            ['code' => '6305', 'abkuerzung' => 'W-MGC', 'sap_code' => '0370', 'beschreibung' => 'Graphitausbildung'],
            ['code' => '6305', 'abkuerzung' => 'W-GIJL', 'sap_code' => '0280', 'beschreibung' => 'Sandstellen, Läufer'],
            ['code' => '6305', 'abkuerzung' => 'W-GIJP', 'sap_code' => '0290', 'beschreibung' => 'Sandstellen, punktuell'],
            ['code' => '6305', 'abkuerzung' => 'W-APR', 'sap_code' => '0500', 'beschreibung' => 'Prüfung'],
        ];

        foreach ($fehlermerkmalStrahlen as $merkmal) {
            FehlermermalStrahlen::create($merkmal);
        }

        // Fehlerorte
        $fehlerorte = [
            ['fehlerkey_sap' => '03', 'name' => 'Festlager'],
            ['fehlerkey_sap' => '02', 'name' => 'Flansch'],
            ['fehlerkey_sap' => '08', 'name' => 'Gehäuseboden'],
            ['fehlerkey_sap' => '04', 'name' => 'Kernbereich'],
            ['fehlerkey_sap' => '05', 'name' => 'Hebeldom'],
            ['fehlerkey_sap' => '07', 'name' => 'Loslager'],
            ['fehlerkey_sap' => '06', 'name' => 'Restliches Bauteil'],
            ['fehlerkey_sap' => '09', 'name' => 'Sattelrücken'],
            ['fehlerkey_sap' => '01', 'name' => 'Zugstrebe'],
        ];

        foreach ($fehlerorte as $ort) {
            Fehlerort::create($ort);
        }

        // Vorgänge
        $vorgaenge = [
            ['id' => 1, 'code' => 5000, 'beschreibung' => 'Strahlen Kleinguss'],
            ['id' => 2, 'code' => 5100, 'beschreibung' => 'Hängebahn-Strahlanlagen'],
            ['id' => 3, 'code' => 6000, 'beschreibung' => 'Komissionierungsplatz KG'],
            ['id' => 4, 'code' => 6050, 'beschreibung' => 'Komissionierungsplatz GG'],
            ['id' => 5, 'code' => 6500, 'beschreibung' => 'Komissionierungsplatz GG'],
            ['id' => 6, 'code' => 6100, 'beschreibung' => 'Putzen KG'],
            ['id' => 7, 'code' => 6300, 'beschreibung' => 'Putzen Extern'],
            ['id' => 8, 'code' => 6600, 'beschreibung' => 'Putzen-Intern GG'],
            ['id' => 9, 'code' => 6700, 'beschreibung' => 'GG Putzen Extern'],
            ['id' => 10, 'code' => 6703, 'beschreibung' => 'QS-Prüfplatz'],
            ['id' => 11, 'code' => 7120, 'beschreibung' => 'Glühofen'],
            ['id' => 12, 'code' => 7250, 'beschreibung' => 'Nachstrahlen Hängebahn-Strahlanlagen'],
            ['id' => 13, 'code' => 8100, 'beschreibung' => 'Lackieren KG'],
            ['id' => 14, 'code' => 8200, 'beschreibung' => 'Lackiern GG'],
        ];

        foreach ($vorgaenge as $vorgang) {
            DB::table('vorgangs')->insert([
                'id' => $vorgang['id'],
                'code' => $vorgang['code'],
                'beschreibung' => $vorgang['beschreibung'],
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        // Kostenstellen
        $kostenstellen = [
            [
                'kostenstelle' => '6912',
                'bezeichnung' => 'Phys. Prüfung/Ausschusserfassung',
                'hierarchie_bereich' => 'OP-AM-3Q',
                'verantwortlicher' => 'Fridrich, Tobias',
            ],
            [
                'kostenstelle' => '6913',
                'bezeichnung' => 'Schweißplatz',
                'hierarchie_bereich' => 'OP-AM-4Q',
                'verantwortlicher' => 'Fridrich, Tobias',
            ],
            [
                'kostenstelle' => '6920',
                'bezeichnung' => 'Messraum Gießerei',
                'hierarchie_bereich' => 'OP-AM-Q3',
                'verantwortlicher' => 'Fridrich, Tobias',
            ],
            [
                'kostenstelle' => '6940',
                'bezeichnung' => 'QS Kleinguss',
                'hierarchie_bereich' => 'OP-AM-3Q',
                'verantwortlicher' => 'Fridrich, Tobias',
            ],
            [
                'kostenstelle' => '7000',
                'bezeichnung' => 'Leitung Maschinen-Kernmacherei',
                'hierarchie_bereich' => 'OP-AM-32',
                'verantwortlicher' => 'Kuechle, Joerg',
            ],
            [
                'kostenstelle' => '7003',
                'bezeichnung' => 'Hand-/Großkernfertigung',
                'hierarchie_bereich' => 'OP-AM-41',
                'verantwortlicher' => 'Marantas, Giorgos',
            ],
            [
                'kostenstelle' => '7011',
                'bezeichnung' => 'Sandaufbereitung Cold Box-Kerne',
                'hierarchie_bereich' => 'OP-AM-32',
                'verantwortlicher' => 'Kuechle, Joerg',
            ],
            [
                'kostenstelle' => '7012',
                'bezeichnung' => '1201 Kernschießmaschine Cold Box',
                'hierarchie_bereich' => 'OP-AM-32',
                'verantwortlicher' => 'Kuechle, Joerg',
            ],
            [
                'kostenstelle' => '7013',
                'bezeichnung' => '40 l Kernschießmaschine Cold Box',
                'hierarchie_bereich' => 'OP-AM-32',
                'verantwortlicher' => 'Kuechle, Joerg',
            ],
            [
                'kostenstelle' => '7014',
                'bezeichnung' => '25 l Kernschießmaschine Cold Box',
                'hierarchie_bereich' => 'OP-AM-32',
                'verantwortlicher' => 'Kuechle, Joerg',
            ],
            [
                'kostenstelle' => '7017',
                'bezeichnung' => 'Schlichten/Nachbehandeln/Bohren Cold Box',
                'hierarchie_bereich' => 'OP-AM-41',
                'verantwortlicher' => 'Marantas, Giorgos',
            ],
            [
                'kostenstelle' => '7021',
                'bezeichnung' => 'Kernschießmaschinen CO2',
                'hierarchie_bereich' => 'OP-AM-32',
                'verantwortlicher' => 'Kuechle, Joerg',
            ],
            [
                'kostenstelle' => '7027',
                'bezeichnung' => 'Schlichten/Nachbehandlung CO2',
                'hierarchie_bereich' => 'OP-AM-32',
                'verantwortlicher' => 'Kuechle, Joerg',
            ],
            [
                'kostenstelle' => '7100',
                'bezeichnung' => 'Schmelzbetrieb',
                'hierarchie_bereich' => 'OP-AM-31',
                'verantwortlicher' => 'Konold, Bernhard',
            ],
            [
                'kostenstelle' => '7102',
                'bezeichnung' => 'Ofen- und Pfannenzustellung',
                'hierarchie_bereich' => 'OP-AM-31',
                'verantwortlicher' => 'Konold, Bernhard',
            ],
            [
                'kostenstelle' => '7111',
                'bezeichnung' => 'Labor Metallurgie',
                'hierarchie_bereich' => 'OP-AM-31',
                'verantwortlicher' => 'Konold, Bernhard',
            ],
            [
                'kostenstelle' => '7200',
                'bezeichnung' => 'Ltg. GG-Formanlage',
                'hierarchie_bereich' => 'OP-AM-42',
                'verantwortlicher' => 'Marantas, Giorgos',
            ],
            [
                'kostenstelle' => '7201',
                'bezeichnung' => 'GRG-Formanlage',
                'hierarchie_bereich' => 'OP-AM-42',
                'verantwortlicher' => 'Marantas, Giorgos',
            ],
            [
                'kostenstelle' => '7202',
                'bezeichnung' => 'GRG-Gieß-Strecke/Formenpuffer',
                'hierarchie_bereich' => 'OP-AM-42',
                'verantwortlicher' => 'Marantas, Giorgos',
            ],
            [
                'kostenstelle' => '7203',
                'bezeichnung' => 'GRG-Ballenkühlbahn',
                'hierarchie_bereich' => 'OP-AM-42',
                'verantwortlicher' => 'Marantas, Giorgos',
            ],
            [
                'kostenstelle' => '7250',
                'bezeichnung' => 'Ltg. Handformerei',
                'hierarchie_bereich' => 'OP-AM-43',
                'verantwortlicher' => 'Marantas, Giorgos',
            ],
            [
                'kostenstelle' => '7251',
                'bezeichnung' => 'Großguss Handformen',
                'hierarchie_bereich' => 'OP-AM-43',
                'verantwortlicher' => 'Marantas, Giorgos',
            ],
            [
                'kostenstelle' => '7300',
                'bezeichnung' => 'KG-Formanlage',
                'hierarchie_bereich' => 'OP-AM-33',
                'verantwortlicher' => 'Haas, Johann',
            ],
            [
                'kostenstelle' => '7399',
                'bezeichnung' => 'Sandlabor',
                'hierarchie_bereich' => 'OP-AM-33',
                'verantwortlicher' => 'Fridrich, Tobias',
            ],
            [
                'kostenstelle' => '7501',
                'bezeichnung' => 'Auspackstrahlanlage',
                'hierarchie_bereich' => 'OP-AM-44',
                'verantwortlicher' => 'Van de Geer, Wilco',
            ],
            [
                'kostenstelle' => '7502',
                'bezeichnung' => 'Großguss entsorgen',
                'hierarchie_bereich' => 'OP-AM-44',
                'verantwortlicher' => 'Van de Geer, Wilco',
            ],
            [
                'kostenstelle' => '7503',
                'bezeichnung' => 'Clansman Manipulator',
                'hierarchie_bereich' => 'OP-AM-44',
                'verantwortlicher' => 'Van de Geer, Wilco',
            ],
            [
                'kostenstelle' => '7550',
                'bezeichnung' => 'Kleinguss Vorbehandlung Plattenband 1',
                'hierarchie_bereich' => 'OP-AM-34',
                'verantwortlicher' => 'Bott, Heiko',
            ],
            [
                'kostenstelle' => '7600',
                'bezeichnung' => 'Ltg. Nachbehandlung & Lackieren',
                'hierarchie_bereich' => 'OP-AM-44',
                'verantwortlicher' => 'Maurer, Maximilian',
            ],
            [
                'kostenstelle' => '7800',
                'bezeichnung' => 'Grundieren',
                'hierarchie_bereich' => 'OP-AM-44',
                'verantwortlicher' => 'Maurer, Maximilian',
            ],
            [
                'kostenstelle' => '7950',
                'bezeichnung' => 'Glühen',
                'hierarchie_bereich' => 'OP-AM-34',
                'verantwortlicher' => 'Werner, Michael',
            ],
        ];

        foreach ($kostenstellen as $kostenstelle) {
            \App\Models\Kostenstelle::create($kostenstelle);
        }

        // Ursachen
        $ursachen = [
            ['kategorie' => 'Gussform/Modell', 'code' => 3101, 'beschreibung' => 'Anschnitt'],
            ['kategorie' => 'Gussform/Modell', 'code' => 3102, 'beschreibung' => 'Form gedrückt'],
            ['kategorie' => 'Gussform/Modell', 'code' => 3104, 'beschreibung' => 'Risse in der Form'],
            ['kategorie' => 'Gussform/Modell', 'code' => 3106, 'beschreibung' => 'Formstoff ungenügend getrocknet'],
            ['kategorie' => 'Gussform/Modell', 'code' => 3107, 'beschreibung' => 'Formstoff ungenügend verdichtet'],
            ['kategorie' => 'Gussform/Modell', 'code' => 3109, 'beschreibung' => 'Formstoff zu geringe Festigkeit'],
            ['kategorie' => 'Gussform/Modell', 'code' => 3110, 'beschreibung' => 'Formstoff zu hoch verdichtet'],
            ['kategorie' => 'Gussform/Modell', 'code' => 3112, 'beschreibung' => 'Luftabführung'],
            ['kategorie' => 'Gussform/Modell', 'code' => 3113, 'beschreibung' => 'Modell beschädigt'],
            ['kategorie' => 'Gussform/Modell', 'code' => 3114, 'beschreibung' => 'Modell verschlissen'],
            ['kategorie' => 'Gussform/Modell', 'code' => 3115, 'beschreibung' => 'Speisung'],
            ['kategorie' => 'Gussform/Modell', 'code' => 3116, 'beschreibung' => 'Einsteckspeiser ausgefallen'],
            ['kategorie' => 'Gussform/Modell', 'code' => 3117, 'beschreibung' => 'Modell fehlerhaft'],
            ['kategorie' => 'Gussform/Modell', 'code' => 3118, 'beschreibung' => 'Kern fehlerhaft'],
            ['kategorie' => 'Gussform/Modell', 'code' => 3119, 'beschreibung' => 'Form beschädigt'],
            ['kategorie' => 'Gussform/Modell', 'code' => 3120, 'beschreibung' => 'Kerneinrichtung verschlissen'],
            ['kategorie' => 'Gussform/Modell', 'code' => 3121, 'beschreibung' => 'Kerneinrichtung beschädigt'],
            ['kategorie' => 'Material', 'code' => 3409, 'beschreibung' => 'Eisenanalyse nicht in Ordnung'],
            ['kategorie' => 'Material', 'code' => 3410, 'beschreibung' => 'Falsche Eisenmarke'],
            ['kategorie' => 'Material', 'code' => 3411, 'beschreibung' => 'Flüssigeisen mit Schlacke'],
            ['kategorie' => 'Material', 'code' => 3412, 'beschreibung' => 'Giesstemperatur zu hoch'],
            ['kategorie' => 'Material', 'code' => 3413, 'beschreibung' => 'Giesstemperatur zu niedrig'],
            ['kategorie' => 'Prozess', 'code' => 3310, 'beschreibung' => 'Transport'],
            ['kategorie' => 'Prozess', 'code' => 3313, 'beschreibung' => 'Anlagenbedingt'],
            ['kategorie' => 'Prozess', 'code' => 3315, 'beschreibung' => 'Form ungenügend abgedichtet'],
            ['kategorie' => 'Prozess', 'code' => 3318, 'beschreibung' => 'Giessgewicht nicht in Ordnung'],
            ['kategorie' => 'Prozess', 'code' => 3319, 'beschreibung' => 'Impfung nicht in Ordnung'],
            ['kategorie' => 'Prozess', 'code' => 3321, 'beschreibung' => 'Nachgegossen'],
            ['kategorie' => 'Prozess', 'code' => 3322, 'beschreibung' => 'Schlichte'],
            ['kategorie' => 'Prozess', 'code' => 3324, 'beschreibung' => 'Schweissen nicht in Ordnung'],
            ['kategorie' => 'Prozess', 'code' => 3325, 'beschreibung' => 'Siebkernbruch'],
            ['kategorie' => 'Prozess', 'code' => 3327, 'beschreibung' => 'Unruhe durch Kernstütze'],
            ['kategorie' => 'Prozess', 'code' => 3329, 'beschreibung' => 'Verstrahlt'],
            ['kategorie' => 'Prozess', 'code' => 3330, 'beschreibung' => 'Wärmebehandlung'],
            ['kategorie' => 'Prozess', 'code' => 3331, 'beschreibung' => 'Zu früh ausgeleert'],
            ['kategorie' => 'Prozess', 'code' => 3332, 'beschreibung' => 'Ölschnur nicht in Ordnung'],
            ['kategorie' => 'Prozess', 'code' => 3333, 'beschreibung' => 'Abgießen nicht in Ordnung'],
            ['kategorie' => 'Prozess', 'code' => 3334, 'beschreibung' => 'Luftschlag'],
            ['kategorie' => 'Prozess', 'code' => 3335, 'beschreibung' => 'Schwindung'],
            ['kategorie' => 'Prozess', 'code' => 3336, 'beschreibung' => 'Giesszeit nicht in Ordnung'],
            ['kategorie' => 'Prozess', 'code' => 3337, 'beschreibung' => 'Unruhe durch Kokille'],
            ['kategorie' => 'Prozess', 'code' => 3338, 'beschreibung' => 'Kokille ausgefallen'],
            ['kategorie' => 'Mitarbeiter', 'code' => 3515, 'beschreibung' => 'Putzer extern'],
            ['kategorie' => 'Mitarbeiter', 'code' => 3516, 'beschreibung' => 'Grundierer extern'],
            ['kategorie' => 'Mitarbeiter', 'code' => 3602, 'beschreibung' => 'Arbeitsplanung'],
            ['kategorie' => 'Mitarbeiter', 'code' => 3603, 'beschreibung' => 'Ausleerer'],
            ['kategorie' => 'Mitarbeiter', 'code' => 3604, 'beschreibung' => 'Former'],
            ['kategorie' => 'Mitarbeiter', 'code' => 3605, 'beschreibung' => 'Giesser'],
            ['kategorie' => 'Mitarbeiter', 'code' => 3606, 'beschreibung' => 'Grundierer'],
            ['kategorie' => 'Mitarbeiter', 'code' => 3607, 'beschreibung' => 'Kernmacher'],
            ['kategorie' => 'Mitarbeiter', 'code' => 3608, 'beschreibung' => 'Modellbau'],
            ['kategorie' => 'Mitarbeiter', 'code' => 3609, 'beschreibung' => 'Prüfer'],
            ['kategorie' => 'Mitarbeiter', 'code' => 3610, 'beschreibung' => 'Putzer intern'],
            ['kategorie' => 'Mitarbeiter', 'code' => 3611, 'beschreibung' => 'Säger'],
            ['kategorie' => 'Mitarbeiter', 'code' => 3612, 'beschreibung' => 'Versandmitarbeiter'],
            ['kategorie' => 'Sonstiges', 'code' => 3704, 'beschreibung' => 'Ursache nicht definierbar'],
            ['kategorie' => 'Sonstiges', 'code' => 3705, 'beschreibung' => 'Verschrottung'],
            ['kategorie' => 'Sonstiges', 'code' => 3710, 'beschreibung' => 'Versuche'],
            ['kategorie' => 'Sonstiges', 'code' => 3711, 'beschreibung' => 'zerstörende Prüfung'],
        ];

        foreach ($ursachen as $ursache) {
            Ursache::create($ursache);
        }
    }
}
