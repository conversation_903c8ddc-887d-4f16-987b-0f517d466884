@tailwind base;
@tailwind components;
@tailwind utilities;

[x-cloak] {
    display: none;
}

/* iOS Form Fixes */
.min-h-screen {
    min-height: -webkit-fill-available;
}

form input[type="email"],
form input[type="password"],
form input[type="text"] {
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
    border-radius: 0.375rem !important;
    font-size: 16px !important;
    padding: 0.5rem 0.75rem !important;
    width: 100% !important;
    border: 1px solid #d1d5db !important;
    background-color: #ffffff !important;
    color: #111827 !important;
    line-height: 1.25 !important;
}

form input[type="email"]:disabled,
form input[type="password"]:disabled,
form input[type="text"]:disabled {
    opacity: 1 !important;
    -webkit-text-fill-color: currentColor !important;
    background-color: #f3f4f6 !important;
}

form button[type="submit"] {
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
    opacity: 1 !important;
}

/* <PERSON> Strahlen */
.theme-strahlen {
    @apply bg-gradient-to-br from-white to-green-50;
    border: 1px solid rgba(34, 139, 34, 0.8); /* <PERSON> Green mit 80% Deckkraft */
}

.theme-strahlen button.btn-primary {
    @apply bg-blue-500 hover:bg-blue-600 text-white;
}

.theme-strahlen button.btn-secondary {
    @apply bg-blue-100 hover:bg-blue-200 text-blue-700;
}

/* Theme Putzen */
.theme-putzen {
    @apply bg-gradient-to-br from-white to-red-50;
    border: 1px solid rgbargba(220, 20, 60, 0.8); /* Crimson Red mit 80% Deckkraft */
}

.theme-putzen label {
    @apply text-red-800;
}

.theme-putzen input:focus,
.theme-putzen select:focus {
    @apply ring-red-500 border-red-500;
}

.theme-putzen button:not([disabled]) {
    @apply hover:bg-red-600 hover:text-white transition-colors;
}

.theme-putzen .error-list {
    @apply bg-red-50;
}

.custom-offset {
    margin-top: 1em; /* Verschiebung Inhalt zu header Abstand*/
}

/* Mobile Input Fixes */
input, 
select, 
textarea {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border-radius: 0.375rem;
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    background-color: #ffffff;
}

/* Fix for iOS zoom on input focus */
@media screen and (-webkit-min-device-pixel-ratio: 0) { 
    input,
    select,
    textarea {
        font-size: 16px !important;
    }
}

/* Fix for iOS disabled inputs */
input:disabled,
select:disabled,
textarea:disabled {
    opacity: 1;
    -webkit-text-fill-color: currentColor;
}

/* Fix for iOS input shadows */
input,
select,
textarea {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

/* Fix for iOS button opacity */
button {
    opacity: 1 !important;
}

/* Fix for iOS tap highlight */
* {
    -webkit-tap-highlight-color: transparent;
}
