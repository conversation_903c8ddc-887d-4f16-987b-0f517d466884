<x-app-layout>
    <x-slot name="header">
        <div class="flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                Daten<PERSON><PERSON>icht
            </h2>
            <div class="flex flex-col sm:flex-row gap-4 w-full sm:w-auto">
                <a href="{{ route('daten.export.csv') }}" class="flex justify-center items-center px-6 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-lg shadow-sm text-base sm:text-sm w-full sm:w-auto">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                    </svg>
                    CSV Export
                </a>
                <a href="{{ route('daten.export.json') }}" target="_blank" class="flex justify-center items-center px-6 py-3 bg-green-500 hover:bg-green-600 text-white rounded-lg shadow-sm text-base sm:text-sm w-full sm:w-auto">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"/>
                    </svg>
                    JSON Export
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
            <!-- Diagramme --> Grösse ändern Arif
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6 mb-6">
                <h3 class="text-lg font-semibold mb-4">Grafische Auswertung</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-4xl mx-auto">
                    <!-- Typ-Verteilung -->
                    <div class="bg-white p-3 rounded-lg shadow">
                        <h4 class="text-sm font-medium text-gray-700 mb-1">Verteilung nach Typ</h4>
                        <div style="height: 150px;">
                            <canvas id="typChart"></canvas>
                        </div>
                    </div>

                    <!-- Fehlerort-Verteilung -->
                    <div class="bg-white p-3 rounded-lg shadow">
                        <h4 class="text-sm font-medium text-gray-700 mb-1">Verteilung nach Fehlerort</h4>
                        <div style="height: 150px;">
                            <canvas id="fehlerortChart"></canvas>
                        </div>
                    </div>

                    <!-- Code-Verteilung -->
                    <div class="bg-white p-3 rounded-lg shadow">
                        <h4 class="text-sm font-medium text-gray-700 mb-1">Verteilung nach Code</h4>
                        <div style="height: 150px;">
                            <canvas id="codeChart"></canvas>
                        </div>
                    </div>

                    <!-- Modellnest-Verteilung -->
                    <div class="bg-white p-3 rounded-lg shadow">
                        <h4 class="text-sm font-medium text-gray-700 mb-1">Verteilung nach Modellnest</h4>
                        <div style="height: 150px;">
                            <canvas id="modellnestChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tabelle -->
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                <div class="overflow-x-auto">
                    <table class="min-w-full table-auto">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" onclick="sortTable(0)">
                                    Rückmeldenummer
                                    <span class="ml-1">↕</span>
                                </th>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" onclick="sortTable(1)">
                                    Typ
                                    <span class="ml-1">↕</span>
                                </th>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" onclick="sortTable(2)">
                                    Modellnest
                                    <span class="ml-1">↕</span>
                                </th>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" onclick="sortTable(3)">
                                    Fehlerkey SAP
                                    <span class="ml-1">↕</span>
                                </th>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" onclick="sortTable(4)">
                                    Fehlerort
                                    <span class="ml-1">↕</span>
                                </th>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" onclick="sortTable(5)">
                                    Code
                                    <span class="ml-1">↕</span>
                                </th>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" onclick="sortTable(6)">
                                    SAP-Code
                                    <span class="ml-1">↕</span>
                                </th>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" onclick="sortTable(7)">
                                    Abkürzung
                                    <span class="ml-1">↕</span>
                                </th>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" onclick="sortTable(8)">
                                    Fehlermerkmal
                                    <span class="ml-1">↕</span>
                                </th>
                                <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" onclick="sortTable(9)">
                                    Menge
                                    <span class="ml-1">↕</span>
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @foreach($buchungen as $buchung)
                                <tr class="hover:bg-gray-50">
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900">{{ $buchung->rueckmeldenummer }}</td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900">{{ $buchung->type }}</td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900">{{ $buchung->modellnest }}</td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900">{{ $buchung->fehlerkey_sap }}</td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900">{{ $buchung->fehlerort_name }}</td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900">{{ $buchung->fehlermerkmal_code }}</td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900">{{ $buchung->fehlermerkmal_sap_code }}</td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900">{{ $buchung->fehlermerkmal_abkuerzung }}</td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900">{{ $buchung->fehlermerkmal_beschreibung }}</td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900">{{ $buchung->menge }}</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Chart.js einbinden -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Sortier-Funktion -->
    <script>
        function sortTable(n) {
            var table, rows, switching, i, x, y, shouldSwitch, dir, switchcount = 0;
            table = document.querySelector("table");
            switching = true;
            dir = "asc";

            while (switching) {
                switching = false;
                rows = table.rows;

                for (i = 1; i < (rows.length - 1); i++) {
                    shouldSwitch = false;
                    x = rows[i].getElementsByTagName("TD")[n];
                    y = rows[i + 1].getElementsByTagName("TD")[n];

                    // Überprüfen, ob es sich um eine Zahl handelt
                    if (!isNaN(x.innerHTML) && !isNaN(y.innerHTML)) {
                        if (dir == "asc") {
                            if (Number(x.innerHTML) > Number(y.innerHTML)) {
                                shouldSwitch = true;
                                break;
                            }
                        } else if (dir == "desc") {
                            if (Number(x.innerHTML) < Number(y.innerHTML)) {
                                shouldSwitch = true;
                                break;
                            }
                        }
                    } else {
                        if (dir == "asc") {
                            if (x.innerHTML.toLowerCase() > y.innerHTML.toLowerCase()) {
                                shouldSwitch = true;
                                break;
                            }
                        } else if (dir == "desc") {
                            if (x.innerHTML.toLowerCase() < y.innerHTML.toLowerCase()) {
                                shouldSwitch = true;
                                break;
                            }
                        }
                    }
                }

                if (shouldSwitch) {
                    rows[i].parentNode.insertBefore(rows[i + 1], rows[i]);
                    switching = true;
                    switchcount++;
                } else {
                    if (switchcount == 0 && dir == "asc") {
                        dir = "desc";
                        switching = true;
                    }
                }
            }
        }
    </script>

    <!-- Diagramme initialisieren -->
    <script>
        // Daten für die Diagramme vorbereiten
        const buchungen = @json($buchungen);

        // Typ-Verteilung
        const typData = buchungen.reduce((acc, buchung) => {
            acc[buchung.type] = (acc[buchung.type] || 0) + buchung.menge;
            return acc;
        }, {});

        new Chart(document.getElementById('typChart'), {
            type: 'pie',
            data: {
                labels: Object.keys(typData),
                datasets: [{
                    data: Object.values(typData),
                    backgroundColor: ['#3B82F6', '#EF4444']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: true,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            boxWidth: 12,
                            padding: 8,
                            font: {
                                size: 10
                            }
                        }
                    }
                }
            }
        });

        // Fehlerort-Verteilung
        const fehlerortData = buchungen.reduce((acc, buchung) => {
            acc[buchung.fehlerort_name] = (acc[buchung.fehlerort_name] || 0) + buchung.menge;
            return acc;
        }, {});

        new Chart(document.getElementById('fehlerortChart'), {
            type: 'bar',
            data: {
                labels: Object.keys(fehlerortData),
                datasets: [{
                    label: 'Menge',
                    data: Object.values(fehlerortData),
                    backgroundColor: '#60A5FA'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: true,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            font: {
                                size: 9
                            },
                            maxRotation: 45,
                            minRotation: 45
                        }
                    },
                    y: {
                        ticks: {
                            font: {
                                size: 9
                            }
                        }
                    }
                }
            }
        });

        // Code-Verteilung
        const codeData = buchungen.reduce((acc, buchung) => {
            acc[buchung.fehlermerkmal_code] = (acc[buchung.fehlermerkmal_code] || 0) + buchung.menge;
            return acc;
        }, {});

        new Chart(document.getElementById('codeChart'), {
            type: 'doughnut',
            data: {
                labels: Object.keys(codeData),
                datasets: [{
                    data: Object.values(codeData),
                    backgroundColor: ['#34D399', '#F87171', '#FBBF24']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: true,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            font: {
                                size: 11
                            }
                        }
                    }
                }
            }
        });

        // Modellnest-Verteilung
        const modellnestData = buchungen.reduce((acc, buchung) => {
            acc[buchung.modellnest] = (acc[buchung.modellnest] || 0) + buchung.menge;
            return acc;
        }, {});

        new Chart(document.getElementById('modellnestChart'), {
            type: 'bar',
            data: {
                labels: Object.keys(modellnestData),
                datasets: [{
                    label: 'Menge',
                    data: Object.values(modellnestData),
                    backgroundColor: '#A78BFA'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: true,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            font: {
                                size: 11
                            }
                        }
                    },
                    y: {
                        ticks: {
                            font: {
                                size: 11
                            }
                        }
                    }
                }
            }
        });
    </script>
</x-app-layout>
