<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/> <!-- UTF-8 Metatag -->
    <title>Ergebnisbericht #{{ $buchung->id }}</title>
    <style>
        :root {
            --color-primary: #111827;
            --color-secondary: #374151;
            --color-border: #e5e7eb;
            --color-card-background: #f3f4f6;
        }

        body {
            font-family: Arial, sans-serif;
            line-height: 1.2;
            margin: 0; /* <PERSON><PERSON>nder */
            padding: 0; /* Keine <PERSON>nder */
            background-color: white; /* Hintergrund auf weiß gesetzt */
            color: var(--color-primary);
        }

        .container {
            max-width: 100%; /* Volle Breite */
            margin: 0; /* Ke<PERSON> */
            background-color: white; /* Hintergrund auf weiß gesetzt */
            border: none; /* <PERSON><PERSON> */
            padding: 0; /* <PERSON><PERSON> */
        }

        .header {
            display: flex;
            justify-content: space-between; /* Titel links, Erstellt am rechts */
            align-items: center; /* Vertikal zentrieren */
            margin-bottom: 1rem; /* Reduzierter Abstand */
            padding-bottom: 1rem; /* Reduzierter Abstand */
            border-bottom: 1px solid var(--color-border);
        }

        .header h2 {
            margin: 0; /* Abstand entfernen */
            color: var(--color-primary);
            font-size: 0.8rem;
            font-weight: bold;
        }

        .header p {
            margin: 0; /* Abstand entfernen */
            color: var(--color-secondary);
            text-align: right; /* Rechtsbündig ausrichten */
        }

        .card {
            background-color: white;
            border: 1px solid var(--color-border);
            margin-bottom: 1rem; /* Reduzierter Abstand */
            overflow: hidden;
            padding: 1rem;
            display: flex;
            flex-direction: column;
        }

        .card-header {
            background-color: var(--color-card-background);
            padding: 1rem;
            border-bottom: 1px solid var(--color-border);
        }

        .card-title {
            margin: 0;
            font-size: 1rem;
            font-weight: 600;
            color: var(--color-primary);
        }

        .card-content {
            padding: 1rem;
            flex: 1; /* Nimmt den verfügbaren Platz ein */
            display: flex;
            flex-direction: column;
        }

        .data-row {
            display: flex;
            margin-bottom: 0.75rem;
        }

        .data-row:last-child {
            margin-bottom: 0;
        }

        .label {
            font-weight: 500;
            width: 200px;
            color: var(--color-secondary);
        }

        .value {
            color: var(--color-primary);
            flex: 1;
        }

        .preview-image {
    margin-top: auto; /* Bild bleibt am unteren Rand */
    text-align: center; /* Zentriert den Inhalt */
    padding-top: 1rem; /* Abstand nach oben */
}

.preview-image img {
    width: 600px; /* Feste Breite */
    height: 380px; /* Feste Höhe */
    object-fit: contain; /* Skaliert das Bild proportional */
    border-radius: 4px; /* Runde Ecken */
    border: 1px solid var(--color-border); /* Rahmen */
}

.image-grid {
    display: grid; /* Grid-Layout verwenden */
    grid-template-columns: repeat(2, 1fr); /* Maximal 2 Bilder pro Zeile */
    gap: 20px; /* Einheitlicher Abstand zwischen den Bildern */
    margin-top: 1rem; /* Abstand zur oberen Grenze */
}

.image-container {
    display: flex; /* Flexbox für zentriertes Layout */
    justify-content: center; /* Bilder horizontal zentrieren */
    align-items: center; /* Bilder vertikal zentrieren */
    margin: 10px; /* Abstand um jedes Bild */
}

.image-container img {
    width: 400px; /* Feste Breite */
    height: 300px; /* Feste Höhe Erste Seite Bild Arif*/
    object-fit: contain; /* Skaliert das Bild proportional */
    border-radius: 10px; /* Runde Ecken */
    border: 1px solid var(--color-border); /* Rahmen */
}

        /* Seitenumbruch nach jeder Bildergruppe */
        .page-break {
            page-break-after: always; /* Erzwingt einen Seitenumbruch nach dem Element */
        }

        @media print {
            body {
                background-color: white;
                padding: 0; /* Keine Ränder beim Drucken */
            }

            .container {
                border: none;
                padding: 0; /* Keine Ränder beim Drucken */
            }

            .card {
                border: 1px solid var(--color-border);
            }
        }

        @media (max-width: 768px) {
            .data-row {
                flex-direction: column;
            }

            .label {
                width: 100%;
                margin-bottom: 0.25rem;
            }

            .image-grid {
                grid-template-columns: 1fr; /* Eine Spalte auf kleinen Bildschirmen */
            }

            .image-container img {
                width: 100%; /* Volle Breite auf kleinen Bildschirmen */
                height: auto; /* Höhe proportional */
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>Ergebnisbericht #{{ $buchung->id }} Erstellt am: {{ now()->format('d.m.Y H:i') }}</h2>
          <p></p> 
        </div>

        <div class="card">
            <div class="card-header">
                <h2 class="card-title">Allgemeine Informationen</h2>
            </div>
            <div class="card-content">
                <div class="data-row">
                    <span class="label">Rückmeldenummer:</span>
                    <span class="value">{{ $buchung->rueckmeldenummer }}</span>
                </div>
                <div class="data-row">
                    <span class="label">Typ:</span>
                    <span class="value">{{ ucfirst($buchung->type) }}</span>
                </div>
                <div class="data-row">
                    <span class="label">Losgröße:</span>
                    <span class="value">{{ $losgroesse }}</span>
                </div>
                <div class="data-row">
                    <span class="label">Gesamtmenge Ausschuss:</span>
                    <span class="value" style="color: red;">{{ $totalMenge }}</span>
                </div>
                <div class="data-row">
                    <span class="label">Ausschussquote:</span>
                    <span class="value" style="color: red;">{{ $ausschussquote }}%</span>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h2 class="card-title">Fehlerdaten</h2>
            </div>
            <div class="card-content">
                @if($buchung->fehlermerkmal)
                <div class="data-row">
                    <span class="label">Fehlermerkmal:</span>
                    <span class="value">{{ $buchung->fehlermerkmal->beschreibung }}</span>
                </div>
                <div class="data-row">
                    <span class="label">Modellnest:</span>
                    <span class="value">{{ $buchung->modellnest }}</span>
                </div>
                @endif
                
                @if($buchung->fehlerort)
                <div class="data-row">
                    <span class="label">Fehlerort:</span>
                    <span class="value">{{ $buchung->fehlerort->name }}</span>
                </div>
                @endif

                <!-- Vorschaugrafik des Hauptbildes -->
                @if(count($images) > 0)
                <div class="preview-image">
                    <img src="{{ $images[0] }}" alt="Vorschaubild">
                </div>
                @endif
            </div>
        </div>

        <!-- Weitere Bilder -->
        @if(count($images) > 1)
        <div class="card">
            <div class="card-header">
                <h2 class="card-title">Weitere Bilder</h2>
            </div>
            <div class="card-content">
                <div class="image-grid">
                    @foreach($images as $key => $image)
                        @if($key > 0) <!-- Überspringe das erste Bild, da es bereits als Vorschau angezeigt wird -->
                        <div class="image-container">
                            <img src="{{ $image }}" alt="Bild {{ $loop->iteration }}">
                        </div>
                        @endif
                        @if(($key > 0) && (($key - 1) % 2 == 1)) <!-- Seitenumbruch nach jedem zweiten Bild -->
                            </div>
                            </div>
                            </div>
                            <div class="page-break"></div> <!-- Seitenumbruch -->
                            <div class="card">
                                <div class="card-header">
                                    <h2 class="card-title">Weitere Bilder (Fortsetzung)</h2>
                                </div>
                                <div class="card-content">
                                    <div class="image-grid">
                        @endif
                    @endforeach
                </div>
            </div>
        </div>
        @endif
    </div>
</body>
</html>