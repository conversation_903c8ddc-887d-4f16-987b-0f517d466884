<div class="min-h-screen bg-gray-100 py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="bg-white rounded-lg shadow-xl p-6">
            <h1 class="text-2xl font-bold text-gray-900 mb-6">Konfiguration</h1>

            <!-- Database Management Section -->
            <div class="mb-8 p-4 bg-gray-50 rounded-lg">
                <h2 class="text-lg font-semibold text-gray-700 mb-4">Datenbank-Management</h2>
                <div class="flex flex-wrap gap-4">
                    <button
                        wire:click="confirmTruncateTable('ausschussbuchungen_sap')"
                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                    >
                        <svg class="mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                        Ausschussbuchungen SAP leeren
                    </button>
                    <button
                        wire:click="confirmTruncateTable('vorlaeufige_ausschussbuchungen')"
                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                    >
                        <svg class="mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                        Vorläufige Ausschussbuchungen leeren
                    </button>
                </div>
            </div>

            <!-- Image Management Section -->
            <div class="mb-8 p-4 bg-gray-50 rounded-lg">
                <h2 class="text-lg font-semibold text-gray-700 mb-4">Bilder-Management</h2>
                <div class="flex flex-wrap gap-4">
                    <button
                        wire:click="confirmImageDelete('all')"
                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                    >
                        <svg class="mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                        Alle Bilder löschen
                    </button>
                </div>
            </div>

            @if (session()->has('message'))
                <div class="mb-4 p-4 bg-green-100 text-green-700 rounded-lg">
                    {{ session('message') }}
                </div>
            @endif

            @if (session()->has('error'))
                <div class="mb-4 p-4 bg-red-100 text-red-700 rounded-lg">
                    {{ session('error') }}
                </div>
            @endif

            <!-- Database Truncate Confirmation Modal -->
            @if($showConfirmModal)
                <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity z-50"></div>
                <div class="fixed inset-0 z-50 overflow-y-auto">
                    <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
                        <div class="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
                            <div class="sm:flex sm:items-start">
                                <div class="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                                    <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
                                    </svg>
                                </div>
                                <div class="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left">
                                    <h3 class="text-lg font-medium leading-6 text-gray-900">
                                        Daten löschen
                                    </h3>
                                    <div class="mt-2">
                                        <p class="text-sm text-gray-500">
                                            Möchten Sie wirklich die ausgewählten Daten löschen? Diese Aktion kann nicht rückgängig gemacht werden.
                                        </p>
                                        
                                        <!-- Zeitrahmen Auswahl -->
                                        <div class="mt-4">
                                            <label for="deleteTimeframeDb" class="block text-sm font-medium text-gray-700">Zeitrahmen</label>
                                            <select wire:model.live="deleteTimeframe" id="deleteTimeframeDb" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                                                <option value="all">Alle Daten</option>
                                                <option value="today">Heute</option>
                                                <option value="yesterday">Gestern</option>
                                                <option value="this_week">Diese Woche</option>
                                                <option value="last_week">Letzte Woche</option>
                                                <option value="this_month">Dieser Monat</option>
                                                <option value="last_month">Letzter Monat</option>
                                                <option value="this_year">Dieses Jahr</option>
                                                <option value="last_year">Letztes Jahr</option>
                                                <option value="custom">Benutzerdefinierter Zeitraum</option>
                                            </select>
                                        </div>

                                        <!-- Benutzerdefinierter Zeitraum -->
                                        @if($deleteTimeframe === 'custom')
                                            <div class="mt-4 grid grid-cols-2 gap-4">
                                                <div>
                                                    <label for="customStartDateDb" class="block text-sm font-medium text-gray-700">Von</label>
                                                    <input type="date" wire:model="customStartDate" id="customStartDateDb" 
                                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                                                </div>
                                                <div>
                                                    <label for="customEndDateDb" class="block text-sm font-medium text-gray-700">Bis</label>
                                                    <input type="date" wire:model="customEndDate" id="customEndDateDb"
                                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                                                </div>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                            <div class="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
                                <button wire:click="truncateTable" type="button" class="inline-flex w-full justify-center rounded-md border border-transparent bg-red-600 px-4 py-2 text-base font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 sm:ml-3 sm:w-auto sm:text-sm">
                                    Löschen
                                </button>
                                <button wire:click="cancelTruncate" type="button" class="mt-3 inline-flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:mt-0 sm:w-auto sm:text-sm">
                                    Abbrechen
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Image Delete Confirmation Modal -->
            @if($showImageDeleteModal)
                <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity z-50"></div>
                <div class="fixed inset-0 z-50 overflow-y-auto">
                    <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
                        <div class="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
                            <div class="sm:flex sm:items-start">
                                <div class="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                                    <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
                                    </svg>
                                </div>
                                <div class="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left">
                                    <h3 class="text-lg font-medium leading-6 text-gray-900">
                                        Bilder löschen
                                    </h3>
                                    <div class="mt-2">
                                        <p class="text-sm text-gray-500">
                                            Möchten Sie wirklich die ausgewählten Bilder löschen? Diese Aktion kann nicht rückgängig gemacht werden.
                                        </p>

                                        <!-- Zeitrahmen Auswahl -->
                                        <div class="mt-4">
                                            <label for="deleteTimeframeImg" class="block text-sm font-medium text-gray-700">Zeitrahmen</label>
                                            <select wire:model.live="deleteTimeframe" id="deleteTimeframeImg" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                                                <option value="all">Alle Bilder</option>
                                                <option value="today">Heute</option>
                                                <option value="yesterday">Gestern</option>
                                                <option value="this_week">Diese Woche</option>
                                                <option value="last_week">Letzte Woche</option>
                                                <option value="this_month">Dieser Monat</option>
                                                <option value="last_month">Letzter Monat</option>
                                                <option value="this_year">Dieses Jahr</option>
                                                <option value="last_year">Letztes Jahr</option>
                                                <option value="custom">Benutzerdefinierter Zeitraum</option>
                                            </select>
                                        </div>

                                        <!-- Benutzerdefinierter Zeitraum -->
                                        @if($deleteTimeframe === 'custom')
                                            <div class="mt-4 grid grid-cols-2 gap-4">
                                                <div>
                                                    <label for="customStartDateImg" class="block text-sm font-medium text-gray-700">Von</label>
                                                    <input type="date" wire:model="customStartDate" id="customStartDateImg" 
                                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                                                </div>
                                                <div>
                                                    <label for="customEndDateImg" class="block text-sm font-medium text-gray-700">Bis</label>
                                                    <input type="date" wire:model="customEndDate" id="customEndDateImg"
                                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                                                </div>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                            <div class="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
                                <button wire:click="deleteImages" type="button" class="inline-flex w-full justify-center rounded-md border border-transparent bg-red-600 px-4 py-2 text-base font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 sm:ml-3 sm:w-auto sm:text-sm">
                                    Löschen
                                </button>
                                <button wire:click="cancelImageDelete" type="button" class="mt-3 inline-flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:mt-0 sm:w-auto sm:text-sm">
                                    Abbrechen
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Search Bar -->
            <div class="mb-6">
                <div class="relative">
                    <input type="text"
                           wire:model.live="search"
                           class="w-full pl-10 pr-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                           placeholder="Suchen...">
                    <div class="absolute left-3 top-2.5">
                        <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                        </svg>
                    </div>
                </div>
            </div>

            <!-- After the search bar and before the tabs -->
 <!--            <div class="mb-6 flex justify-end space-x-4">
                <a href="{{ route('daten.export.csv') }}"
                   class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <svg class="mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                    </svg>
                    CSV Export
                </a>

                <a href="{{ route('daten.export.json') }}"
                   class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                    <svg class="mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                    </svg>
                    JSON Export
                </a>
            </div> -->

            <!-- Tabs - Now with horizontal scrolling -->
            <div class="mb-6 border-b border-gray-200 overflow-x-auto">
                <div class="flex whitespace-nowrap min-w-full">
                    @foreach([
                        'fehlermerkmal_normal' => 'Fehlermerkmal Normal',
                        'fehlermerkmal_putzen' => 'Fehlermerkmal Putzen',
                        'fehlermerkmal_strahlen' => 'Fehlermerkmal Strahlen',
                        'fehlerort' => 'Fehlerort',
                        'kostenstelle' => 'Kostenstelle',
                        'ursache' => 'Ursache',
                        'vorgang' => 'Vorgang',
                        'zuordnung' => 'Zuordnung'
                    ] as $tab => $label)
                        <button
                            wire:click="setActiveTab('{{ $tab }}')"
                            class="inline-flex items-center px-4 py-2 border-b-2 text-sm font-medium leading-5 focus:outline-none transition duration-150 ease-in-out min-w-max {{ $activeTab === $tab ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }}"
                        >
                            {{ $label }}
                        </button>
                    @endforeach
                </div>
            </div>

            <!-- Responsive Table Container -->
            <div class="overflow-x-auto -mx-4 sm:-mx-6 lg:-mx-8">
                <div class="inline-block min-w-full py-2 align-middle px-4 sm:px-6 lg:px-8">
                    <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 rounded-lg">
                        <table class="min-w-full divide-y divide-gray-300">
                            <thead class="bg-gray-50">
                                <tr>
                                    @switch($activeTab)
                                        @case('fehlermerkmal_normal')
                                        @case('fehlermerkmal_putzen')
                                        @case('fehlermerkmal_strahlen')
                                            <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">Code</th>
                                            <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">SAP Code</th>
                                            <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Abkürzung</th>
                                            <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Beschreibung</th>
                                            @break

                                        @case('fehlerort')
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fehlerkey SAP</th>
                                            @break

                                        @case('kostenstelle')
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Kostenstelle</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bezeichnung</th>
                                            @break

                                        @case('ursache')
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Beschreibung</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Kategorie</th>
                                            @break

                                        @case('vorgang')
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Beschreibung</th>
                                            @break

                                        @case('zuordnung')
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Modellnest</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Lage</th>
                                            @break
                                    @endswitch
                                    <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6">
                                        <span class="sr-only">Aktionen</span>
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200 bg-white">
                                @forelse($data as $item)
                                    <tr class="hover:bg-gray-50">
                                        @switch($activeTab)
                                            @case('fehlermerkmal_normal')
                                            @case('fehlermerkmal_putzen')
                                            @case('fehlermerkmal_strahlen')
                                                <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6">{{ $item->code }}</td>
                                                <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{{ $item->sap_code }}</td>
                                                <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{{ $item->abkuerzung }}</td>
                                                <td class="px-3 py-4 text-sm text-gray-500 max-w-xs truncate">{{ $item->beschreibung }}</td>
                                                @break

                                            @case('fehlerort')
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $item->name }}</td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $item->fehlerkey_sap }}</td>
                                                @break

                                            @case('kostenstelle')
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $item->kostenstelle }}</td>
                                                <td class="px-6 py-4 text-sm text-gray-900">{{ $item->bezeichnung }}</td>
                                                @break

                                            @case('ursache')
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $item->code }}</td>
                                                <td class="px-6 py-4 text-sm text-gray-900">{{ $item->beschreibung }}</td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $item->kategorie }}</td>
                                                @break

                                            @case('vorgang')
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $item->code }}</td>
                                                <td class="px-6 py-4 text-sm text-gray-900">{{ $item->beschreibung }}</td>
                                                @break

                                            @case('zuordnung')
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $item->zuordnungmodellnest }}</td>
                                                <td class="px-6 py-4 text-sm text-gray-900">{{ $item->zuordnunglage }}</td>
                                                @break
                                        @endswitch
                                        <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                                            <button wire:click="edit({{ $item->id }})"
                                                    class="text-blue-600 hover:text-blue-900 focus:outline-none focus:underline">
                                                Bearbeiten
                                            </button>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="4" class="px-6 py-4 text-center text-sm text-gray-500">
                                            Keine Einträge gefunden
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Pagination with responsive margins -->
            <div class="mt-4 px-4 sm:px-6 lg:px-8">
                {{ $data->links() }}
            </div>

            <!-- Edit Modal -->
            @if($isEditing)
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>

            <div class="fixed inset-0 z-10 overflow-y-auto">
                <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
                    <div class="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
                        <div class="absolute right-0 top-0 hidden pr-4 pt-4 sm:block">
                            <button wire:click="$set('isEditing', false)" type="button" class="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                <span class="sr-only">Schließen</span>
                                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>

                        <div class="sm:flex sm:items-start">
                            <div class="mt-3 text-center sm:mt-0 sm:text-left w-full">
                                <h3 class="text-base font-semibold leading-6 text-gray-900">Eintrag bearbeiten</h3>
                                <div class="mt-4 space-y-4">
                                    @switch($activeTab)
                                        @case('fehlermerkmal_normal')
                                        @case('fehlermerkmal_putzen')
                                        @case('fehlermerkmal_strahlen')
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">Code</label>
                                                <input type="text" wire:model="editModel.code" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">SAP Code</label>
                                                <input type="text" wire:model="editModel.sap_code" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">Abkürzung</label>
                                                <input type="text" wire:model="editModel.abkuerzung" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">Beschreibung</label>
                                                <input type="text" wire:model="editModel.beschreibung" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                                            </div>
                                            @break

                                        @case('fehlerort')
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">Name</label>
                                                <input type="text" wire:model="editModel.name" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">Fehlerkey SAP</label>
                                                <input type="text" wire:model="editModel.fehlerkey_sap" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                                            </div>
                                            @break

                                        @case('kostenstelle')
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">Kostenstelle</label>
                                                <input type="text" wire:model="editModel.kostenstelle" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">Bezeichnung</label>
                                                <input type="text" wire:model="editModel.bezeichnung" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                                            </div>
                                            @break

                                        @case('ursache')
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">Code</label>
                                                <input type="text" wire:model="editModel.code" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">Beschreibung</label>
                                                <input type="text" wire:model="editModel.beschreibung" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">Kategorie</label>
                                                <input type="text" wire:model="editModel.kategorie" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                                            </div>
                                            @break

                                        @case('vorgang')
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">Code</label>
                                                <input type="text" wire:model="editModel.code" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">Beschreibung</label>
                                                <input type="text" wire:model="editModel.beschreibung" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                                            </div>
                                            @break

                                        @case('zuordnung')
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">Modellnest</label>
                                                <input type="number" wire:model="editModel.zuordnungmodellnest" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">Lage</label>
                                                <input type="number" wire:model="editModel.zuordnunglage" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                                            </div>
                                            @break
                                    @endswitch
                                </div>
                            </div>
                        </div>

                        <div class="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
                            <button wire:click="save" type="button" class="inline-flex w-full justify-center rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 sm:ml-3 sm:w-auto">
                                Speichern
                            </button>
                            <button wire:click="$set('isEditing', false)" type="button" class="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:mt-0 sm:w-auto">
                                Abbrechen
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            @endif
        </div>
    </div>
</div>
