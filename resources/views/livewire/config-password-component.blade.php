<div class="min-h-screen bg-gray-100 py-6 flex flex-col justify-center sm:py-12">
    <div class="relative py-3 sm:max-w-xl sm:mx-auto">
        <div class="relative px-4 py-10 bg-white shadow-lg sm:rounded-3xl sm:p-20">
            <div class="max-w-md mx-auto">
                <div class="divide-y divide-gray-200">
                    <div class="py-8 text-base leading-6 space-y-4 text-gray-700 sm:text-lg sm:leading-7">
                        <h2 class="text-2xl font-bold mb-8 text-center text-gray-900">Konfiguration Zugriff</h2>

                        @if($error)
                            <div class="text-red-600 text-sm mb-4">
                                {{ $error }}
                            </div>
                        @endif

                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Passwort
                            </label>
                            <input type="password"
                                   wire:model="password"
                                   wire:keydown.enter="checkPassword"
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                                   placeholder="Geben Sie das Passwort ein">
                        </div>

                        <div class="mt-8">
                            <button wire:click="checkPassword"
                                    class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                Zugriff anfordern
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
