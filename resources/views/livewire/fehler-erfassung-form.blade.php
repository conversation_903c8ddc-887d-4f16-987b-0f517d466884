<div class="min-h-screen flex items-center justify-center custom-offset">
    <div class="max-w-4xl mx-auto">
        @if($showTypeSelection)
        <div class="bg-white/80 backdrop-blur-xl p-8 rounded-3xl shadow-2xl mb-8 border border-gray-200/50">
            <h2 class="text-4xl font-extrabold mb-6 text-center bg-gradient-to-r from-indigo-600 to-purple-600 text-transparent bg-clip-text">
                Leseband oder Maus?
            </h2>

            <p class="text-xl text-center mb-10 text-gray-700">
                Sind die Teile gestrahlt, geputzt oder normal? ⁉️
            </p>

            <div class="flex flex-col sm:flex-row justify-center gap-3">
                <button wire:click="setType('strahlen')"
                        class="group relative px-10 py-5 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl text-white font-bold text-lg transition-all duration-300 flex items-center gap-4 overflow-hidden shadow-lg hover:shadow-blue-500/50 hover:scale-105 focus:outline-none focus:ring-4 focus:ring-blue-300">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                    <span>STRAHLEN</span>
                    <div class="absolute inset-0 bg-white/20 translate-y-full group-hover:translate-y-0 transition-transform duration-300"></div>
                </button>

                <button wire:click="setType('putzen')"
                        class="group relative px-10 py-5 bg-gradient-to-br from-red-500 to-pink-600 rounded-2xl text-white font-bold text-lg transition-all duration-300 flex items-center gap-4 overflow-hidden shadow-lg hover:shadow-red-500/50 hover:scale-105 focus:outline-none focus:ring-4 focus:ring-red-300">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                    </svg>
                    <span>PUTZEN</span>
                    <div class="absolute inset-0 bg-white/20 translate-y-full group-hover:translate-y-0 transition-transform duration-300"></div>
                </button>

                <button wire:click="setType('normal')"
                        class="group relative px-10 py-5 bg-gradient-to-br from-emerald-500 to-green-600 rounded-2xl text-white font-bold text-lg transition-all duration-300 flex items-center gap-4 overflow-hidden shadow-lg hover:shadow-emerald-500/50 hover:scale-105 focus:outline-none focus:ring-4 focus:ring-emerald-300">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span>NORMAL</span>
                    <div class="absolute inset-0 bg-white/20 translate-y-full group-hover:translate-y-0 transition-transform duration-300"></div>
                </button>
            </div>

            <div class="mt-6 text-center text-sm text-gray-600">
                <p class="italic">
                    Wähle "NORMAL", wenn du keinen modellbezogenen Ausschuss erfassen möchtest, in der Regel für interne Teile. Es ist jedoch auch möglich, modellbezogenen Ausschuss zu buchen.
                </p>
            </div>
        </div>
    @else
        <div class="bg-white rounded-lg shadow-lg p-6 {{ $type === 'strahlen' ? 'border-4 border-blue-200 bg-blue-50/30' : ($type === 'putzen' ? 'border-4 border-red-200 bg-red-50/30' : 'border-4 border-emerald-200 bg-emerald-50/30') }}">
            <style>
                /* Prevent zoom on number inputs and buttons */
                .no-zoom {
                    touch-action: manipulation;
                    -webkit-touch-callout: none;
                    -webkit-user-select: none;
                    user-select: none;
                }
                .no-zoom input[type="number"] {
                    font-size: 16px !important;
                }
                /* Prevent zoom on spinbutton */
                input[type="number"]::-webkit-outer-spin-button,
                input[type="number"]::-webkit-inner-spin-button {
                    -webkit-appearance: none;
                    margin: 0;
                }
                input[type="number"] {
                    -moz-appearance: textfield;
                }
                /* Custom number input controls */
                .number-input-wrapper {
                    display: flex;
                    align-items: center;
                }
                .number-control {
                    padding: 8px 12px;
                    background-color: #f3f4f6;
                    border: 1px solid #d1d5db;
                    touch-action: manipulation;
                    user-select: none;
                    font-size: 16px;
                }
            </style>
            <!-- Type Indicator with Reset & Submit Buttons -->
            <div class="mb-6 flex justify-between items-center">
                <span class="text-lg font-semibold {{ $type === 'strahlen' ? 'text-blue-600' : ($type === 'putzen' ? 'text-red-600' : 'text-emerald-600') }}">
                    {{ strtoupper($type) }}
                </span>
                <div class="flex items-center gap-2">
                    <!-- Reset Button -->
                    <button wire:click="resetForm"
                            class="p-2 bg-gradient-to-r from-gray-100 to-gray-200 hover:from-gray-200 hover:to-gray-300 text-gray-600 rounded-xl shadow-lg transition-all duration-200 hover:shadow-gray-200/50 hover:-translate-y-0.5 flex items-center justify-center group"
                            title="Formular zurücksetzen">
                        <svg class="w-6 h-6 group-hover:rotate-180 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                        </svg>
                    </button>

                    @if(count($vorlaeufigeBuchungen) > 0)
                        <!-- Submit Button -->
                        <button wire:click="submit"
                                class="p-2 bg-gradient-to-r from-green-400 to-green-500 hover:from-green-500 hover:to-green-600 text-white rounded-xl shadow-lg transition-all duration-200 hover:shadow-green-200/50 hover:-translate-y-0.5 flex items-center justify-center group"
                                title="Absenden">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                        </button>
                    @endif
                </div>
            </div>
            <!-- Rückmeldenummer und Losgröße -->
            <div class="mb-6">
                <div class="flex gap-2">
                    <div class="flex-1">
                        <label class="block text-sm font-medium text-gray-700 mb-1">
                            Rückmeldenummer
                        </label>
                        <input type="text"
                               wire:model="rueckmeldenummer"
                               @if($isRueckmeldenummerLocked) disabled @endif
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100 text-left">
                        @error('rueckmeldenummer') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                    </div>
                    <div class="w-24">
                        <label class="block text-sm font-medium text-gray-700 mb-1">
                            Losgröße
                        </label>
                        <input type="number"
                               wire:model="losgroesse"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100 text-right font-mono"
                               min="1"
                               max="9999"
                               maxlength="4"
                               @if($isRueckmeldenummerLocked) disabled @endif
                               oninput="javascript: if (this.value.length > 4) this.value = this.value.slice(0, 4);"
                               style="width: 6rem;">
                        @error('losgroesse') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                    </div>
                </div>

                <!-- Letzte Rückmeldenummern -->
                @if(count($recentRueckmeldenummern) > 0)
                    <div class="mt-4">
                        <label class="block text-sm font-medium text-gray-700">Letzte Rückmeldenummern:</label>
                        <div class="mt-1 flex flex-wrap gap-2">
                            @foreach($recentRueckmeldenummern as $nummer)
                                <div class="relative group">
                                    <button wire:click="setRueckmeldenummer('{{ $nummer['rueckmeldenummer'] }}')"
                                        class="inline-flex items-center px-3 py-1 rounded-md text-sm
                                        {{ $nummer['status'] === 'vorläufig' ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800' }}">
                                        {{ $nummer['rueckmeldenummer'] }}
                                    </button>
                                    @if($nummer['status'] === 'vorläufig')
                                        <button wire:click="deleteRueckmeldenummer('{{ $nummer['rueckmeldenummer'] }}')"
                                            class="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1
                                            hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                            </svg>
                                        </button>
                                    @endif
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif
            </div>

            <!-- Modellnest -->
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    Modellnest
                </label>
                <!-- Aktueller Block -->
                <div class="grid grid-cols-3 sm:grid-cols-6 gap-2 mb-2">
                    @foreach($modellnestBlocks[$currentBlock] ?? [] as $nest)
                        <button wire:click="$set('selectedModellnest', '{{ $nest }}')"
                                class="px-4 py-3 rounded-md text-center transition-colors duration-200
                                {{ $selectedModellnest === $nest ? 'bg-blue-500 text-white' : 'bg-gray-200 hover:bg-gray-300' }}">
                            {{ $nest }}
                        </button>
                    @endforeach
                </div>

                <!-- Block-Auswahl -->
                <div class="mt-2">
                    <button wire:click="toggleAllModellnests"
                            class="text-sm text-blue-600 hover:text-blue-800 flex items-center gap-1">
                        <span>{{ $showAllModellnests ? 'Weniger anzeigen' : 'Mehr Modellneste anzeigen' }}</span>
                        <svg class="w-4 h-4 transform {{ $showAllModellnests ? 'rotate-180' : '' }}"
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                        </svg>
                    </button>
                </div>

                <!-- Alle Blöcke -->
                @if($showAllModellnests)
                    <div class="mt-4 space-y-4 bg-gray-50 p-4 rounded-lg border border-gray-200">
                        @foreach($modellnestBlocks as $blockIndex => $block)
                            <div class="space-y-2">
                                <div class="text-sm font-medium text-gray-700">Block {{ $blockIndex + 1 }}</div>
                                <div class="grid grid-cols-3 sm:grid-cols-6 gap-2">
                                    @foreach($block as $nest)
                                        <button wire:click="selectBlock({{ $blockIndex }})"
                                                class="px-4 py-3 rounded-md text-center transition-colors duration-200
                                                {{ $currentBlock === $blockIndex ? 'bg-blue-100' : 'bg-white' }}
                                                hover:bg-blue-50 border border-gray-200">
                                            {{ $nest }}
                                        </button>
                                    @endforeach
                                </div>
                            </div>
                        @endforeach
                    </div>
                @endif

                @error('selectedModellnest')
                    <span class="text-red-500 text-sm mt-1 block">{{ $message }}</span>
                @enderror
            </div>

            <!-- Spezielle Felder für NORMAL-Maske -->
            @if($type === 'normal')
            <div class="space-y-6">
                <!-- Vorgang -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        Vorgang
                    </label>
                    <select wire:model="vorgang"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">Bitte wählen...</option>
                        @foreach($vorgaenge as $vorgang)
                            <option value="{{ $vorgang->code }}">
                                {{ $vorgang->code }} - {{ $vorgang->beschreibung }}
                            </option>
                        @endforeach
                    </select>
                    @error('vorgang') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                </div>

                <!-- Kostenstelle -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        Kostenstelle
                    </label>
                    <select wire:model="selectedKostenstelle"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">Bitte wählen...</option>
                        @foreach($kostenstellen as $kostenstelle)
                            <option value="{{ $kostenstelle->id }}">
                                {{ $kostenstelle->kostenstelle }} - {{ $kostenstelle->bezeichnung }}
                            </option>
                        @endforeach
                    </select>
                    @error('selectedKostenstelle') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                </div>

                <!-- Fehlermerkmal für NORMAL-Maske -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        Fehlermerkmal
                    </label>
                    <select wire:model="selectedFehlermerkmal"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">Bitte wählen...</option>
                        @foreach($fehlermerkmale as $merkmal)
                            <option value="{{ $merkmal->id }}">
                                {{ $merkmal->sap_code }} - {{ $merkmal->abkuerzung }} {{ $merkmal->beschreibung }}
                            </option>
                        @endforeach
                    </select>
                    @error('selectedFehlermerkmal') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                </div>

                <!-- Ursache -->
                <div class="mb-8">
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        Ursache
                    </label>
                    <select wire:model="ursache"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">Bitte wählen...</option>
                        @foreach($ursachen->groupBy('kategorie') as $kategorie => $ursachenGruppe)
                            <optgroup label="{{ $kategorie }}">
                                @foreach($ursachenGruppe as $ursache)
                                    <option value="{{ $ursache->code }}">
                                        {{ $ursache->code }} - {{ $ursache->beschreibung }}
                                    </option>
                                @endforeach
                            </optgroup>
                        @endforeach
                    </select>
                    @error('ursache') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                </div>
            @endif

            <!-- Fehlermerkmal für STRAHLEN und PUTZEN -->
            @if($type !== 'normal')
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-1">
                    Fehlermerkmal
                </label>
                <select wire:model="selectedFehlermerkmal"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">Bitte wählen...</option>
                    @foreach($fehlermerkmale as $merkmal)
                        <option value="{{ $merkmal->id }}">
                            {{ $merkmal->sap_code }} - {{ $merkmal->abkuerzung }} {{ $merkmal->beschreibung }}
                        </option>
                    @endforeach
                </select>
                @error('selectedFehlermerkmal') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
            </div>
            @endif

            <!-- Fehlerort - nur für STRAHLEN und PUTZEN -->
            @if($type !== 'normal')
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-1">
                    Fehlerort
                </label>
                <select wire:model="selectedFehlerort"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">Bitte wählen...</option>
                    @foreach($fehlerorte as $ort)
                        <option value="{{ $ort['id'] }}">
                            {{ $ort['name'] }}
                        </option>
                    @endforeach
                </select>
                @error('selectedFehlerort') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
            </div>
            @endif

            <!-- Menge Input with custom controls -->
            <div class="mt-6 no-zoom">
                <label for="menge" class="block text-sm font-semibold text-gray-700 mb-2">
                    Anzahl Fehler
                </label>
                <div class="flex items-center justify-center bg-gray-50 rounded-xl p-4 shadow-inner">
                    <div class="flex items-center gap-4">
                        <button type="button" 
                                class="w-12 h-12 flex items-center justify-center bg-white rounded-xl shadow-lg border border-gray-100 text-gray-500 hover:bg-gray-50 active:bg-gray-100 transition-all duration-200" 
                                wire:click="decrementMenge"
                                aria-label="Decrease">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"/>
                            </svg>
                        </button>
                        
                        <div class="relative w-24">
                            <input type="number" 
                                   id="menge" 
                                   wire:model="menge" 
                                   class="block w-full text-center text-2xl font-bold text-gray-700 bg-white border-2 border-gray-200 rounded-lg py-3 px-2 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200"
                                   min="1"
                                   inputmode="numeric"
                                   pattern="[0-9]*">
                            <div class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                                <!-- <span class="text-gray-400 text-sm">Stk</span> -->
                            </div>
                        </div>
                        
                        <button type="button" 
                                class="w-12 h-12 flex items-center justify-center bg-white rounded-xl shadow-lg border border-gray-100 text-gray-500 hover:bg-gray-50 active:bg-gray-100 transition-all duration-200" 
                                wire:click="incrementMenge"
                                aria-label="Increase">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
                            </svg>
                        </button>
                    </div>
                </div>
                @error('menge') 
                    <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Buttons -->
            <div class="mt-6 flex flex-col sm:flex-row justify-between items-center gap-3">
                <!-- Action Buttons Container -->
                <div class="flex w-full gap-2">
                    <!-- Kommentar Button -->
                    <button wire:click="toggleKommentar"
                            class="flex-1 bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white px-4 py-2 rounded-xl font-medium shadow-lg transition-all duration-200 hover:shadow-gray-200/50 hover:-translate-y-0.5 flex items-center justify-center gap-2">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"/>
                        </svg>
                        <span class="hidden sm:inline">Kommentar</span>
                    </button>

                    <!-- Foto Button -->
                    <button 
                            id="foto-button"
                            x-data="{
                                openPhotoCapture() {
                                    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
                                    
                                    if (isIOS) {
                                        // For iOS, we'll use a temporary input and manually add the photo to the array
                                        // Create a temporary file input
                                        let tempInput = document.createElement('input');
                                        tempInput.type = 'file';
                                        tempInput.id = 'temp-photo-input';
                                        tempInput.className = 'hidden';
                                        tempInput.accept = 'image/*';
                                        tempInput.setAttribute('capture', 'camera');
                                        
                                        // Add change event listener to handle the file
                                        tempInput.addEventListener('change', function(e) {
                                            if (this.files && this.files.length > 0) {
                                                // Use Livewire's upload method to manually add the photo
                                                @this.upload('tempPhoto', this.files[0], (uploadedFilename) => {
                                                    // After successful upload, tell the component to add it to the photos array
                                                    @this.call('addPhotoFromTemp');
                                                }, () => {
                                                    // On upload error
                                                    console.error('Upload failed');
                                                }, (event) => {
                                                    // Progress callback if needed
                                                });
                                            }
                                            // Remove this input after use
                                            setTimeout(() => {
                                                this.remove();
                                            }, 1000);
                                        });
                                        
                                        // Append to body, click, then remove after processing
                                        document.body.appendChild(tempInput);
                                        tempInput.click();
                                    } else {
                                        // For non-iOS devices, use the standard multiple file input
                                        const fileInput = document.getElementById('photo-upload');
                                        fileInput.click();
                                    }
                                }
                            }"
                            x-on:click.prevent="openPhotoCapture()"
                            type="button"
                            class="flex-1 bg-gradient-to-r from-purple-400 to-purple-500 hover:from-purple-500 hover:to-purple-600 text-white px-4 py-2 rounded-xl font-medium shadow-lg transition-all duration-200 hover:shadow-purple-200/50 hover:-translate-y-0.5 flex items-center justify-center gap-2">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"/>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"/>
                        </svg>
                        <span class="hidden sm:inline">Foto</span>
                    </button>
                </div>

                <!-- Speichern Button -->
                <button wire:click="save"
                        class="w-full sm:w-auto bg-gradient-to-r from-blue-400 to-blue-500 hover:from-blue-500 hover:to-blue-600 text-white px-6 py-2 rounded-xl font-medium shadow-lg transition-all duration-200 hover:shadow-blue-200/50 hover:-translate-y-0.5 flex items-center justify-center gap-2">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"/>
                    </svg>
                    Speichern
                </button>
            </div>

            <!-- Hidden file input for photo capture -->
            <div>
                <!-- Standard file input for non-iOS devices -->
                <input type="file" 
                       id="photo-upload"
                       wire:model.live.debounce.500ms="fotos" 
                       accept="image/*" 
                       multiple
                       class="hidden"
                       wire:key="photo-upload-{{ count($fotos) }}">
            </div>

            <!-- Foto Preview Section -->
            @if (!empty($fotos))
            <div class="mt-6 animate-fade-in">
                <div class="flex items-center justify-between mb-2">
                    <label class="block text-sm font-medium text-gray-700">
                        Fotos
                    </label>
                    <span class="text-xs text-gray-500">Tippe auf + für weitere Fotos</span>
                </div>
                
                @error('fotos.*') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror

                <div class="mt-2 grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                    @foreach($fotos as $index => $foto)
                        <div class="relative group">
                            <img src="{{ $foto->temporaryUrl() }}" class="h-32 w-full object-cover rounded">
                            <button wire:click="removeFoto({{ $index }})"
                                    type="button"
                                    class="absolute top-0 right-0 bg-red-500 text-white p-1 rounded-full transform translate-x-1/2 -translate-y-1/2 opacity-80 sm:opacity-0 sm:group-hover:opacity-100 transition-opacity">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                </svg>
                            </button>
                        </div>
                    @endforeach
                    
                    <!-- Add another photo button - Simplified implementation -->
                    <button 
                        type="button"
                        onclick="document.getElementById('foto-button').click()"
                        class="h-32 border-2 border-dashed border-gray-300 rounded flex items-center justify-center cursor-pointer hover:bg-gray-50 transition-colors">
                        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                        </svg>
                    </button>
                </div>
            </div>
            @endif
                
            <!-- Kommentar (nur anzeigen wenn aktiviert) -->
            @if($showKommentar)
                <div class="mt-6 animate-fade-in">
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        Kommentar
                    </label>
                    <textarea wire:model="kommentar"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                              rows="3"></textarea>
                </div>
            @endif

            <!-- Vorläufig gespeicherte Fehler - Immer anzeigen -->
            <div class="mt-6 p-4 bg-yellow-50 border-2 border-yellow-200 rounded-lg">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="font-medium text-yellow-800">Vorläufig gespeicherte Fehler:</h3>
                    @if($rueckmeldenummer)
                        <span class="text-sm text-yellow-600">Rück.-Nr.: {{ $rueckmeldenummer }}</span>
                    @endif
                </div>
                @if(count($vorlaeufigeBuchungen) > 0)
                    @foreach($vorlaeufigeBuchungen as $buchung)
                        <div class="text-sm py-2 border-b border-yellow-200 last:border-0">
                            <div class="flex justify-between items-start group">
                                <div class="flex-grow">
                                    <span class="font-semibold">{{ $buchung->modellnest }}:</span>
                                    {{ $buchung->menge }}x {{ $buchung->fehlermerkmal->abkuerzung }} –
                                    {{ $buchung->fehlermerkmal->beschreibung }}
                                    ({{ $buchung->fehlerort->name }})
                                </div>
                                <div class="flex items-center gap-2">
                                    <div class="text-xs text-yellow-600" style="margin-left:10px">
                                        {{ $buchung->created_at->format('H:i') }}
                                    </div>
                                    <button wire:click="deleteVorlaeufigeBuchung('{{ $buchung->id }}')"
                                            class="text-red-500 hover:text-red-700 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                                            title="Eintrag löschen">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                            @if($buchung->kommentar)
                                <div class="mt-1 text-xs text-yellow-700 italic">
                                    {{ $buchung->kommentar }}
                                </div>
                            @endif
                        </div>
                    @endforeach
                @else
                    <div class="text-sm text-yellow-700 italic">
                        Noch keine Fehler erfasst.
                    </div>
                @endif
            </div>

            <!-- Letzte Rückmeldungen -->
            @if(count($letzteBuchungen) > 0)
                <div class="mt-6 p-4 bg-gray-50 rounded-lg">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="font-medium">Bereits übermittelt:</h3>
                        <span class="text-sm text-yellow-600">Rück.-Nr.: {{ $rueckmeldenummer }}</span>
                        <!-- Rückmeldenummer Anzeige Bereits übermittelte Rückmeldungen:-->

                        {{-- <span class="text-sm text-gray-600">Aktiv: {{ $rueckmeldenummer }}</span> --}}
                    </div>
                    @foreach($letzteBuchungen as $buchung)
                        <div class="text-sm py-2 border-b border-gray-200 last:border-0">
                            <div class="flex justify-between items-start">
                                <div>
                                    <span class="font-semibold">{{ $buchung->modellnest }}:</span>
                                    {{ $buchung->menge }}x {{ $buchung->fehlermerkmal->abkuerzung }}
                                    ({{ $buchung->fehlerort->name }})
                                </div>
                                <div class="text-xs text-gray-500">
                                    {{ $buchung->created_at->format('d.m.Y H:i') }}
                                </div>
                            </div>
                            @if($buchung->kommentar)
                                <div class="mt-1 text-xs text-gray-600 italic">
                                    {{ $buchung->kommentar }}
                                </div>
                            @endif
                        </div>
                    @endforeach
                </div>
            @endif
        </div>
    @endif
</div>
