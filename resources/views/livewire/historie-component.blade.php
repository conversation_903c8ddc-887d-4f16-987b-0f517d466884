<div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8"
     x-data="{
        showLightbox: false,
        showComment: false,
        currentImageIndex: 0,
        scale: 1,
        rotation: 0,
        selectedBuchung: null,
        isDragging: false,
        startX: 0,
        startY: 0,
        translateX: 0,
        translateY: 0,
        init() {
            window.addEventListener('keydown', (e) => {
                if (this.showLightbox) {
                    if (e.key === 'ArrowLeft') this.previousImage();
                    if (e.key === 'ArrowRight') this.nextImage();
                    if (e.key === 'Escape') this.closeLightbox();
                }
            });
        },
        resetTransform() {
            this.scale = 1;
            this.rotation = 0;
            this.translateX = 0;
            this.translateY = 0;
        },
        previousImage() {
            if (this.currentImageIndex > 0) {
                this.currentImageIndex--;
            }
        },
        nextImage() {
            if (this.currentImageIndex < this.selectedBuchung.fotos.length - 1) {
                this.currentImageIndex++;
            }
        },
        closeLightbox() {
            this.showLightbox = false;
            this.resetTransform();
        }
     }">

    <!-- Filter Section -->
    <div class="mb-6 bg-white rounded-lg shadow-sm p-4">
        <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Suche</label>
                <input type="text" wire:model.live.debounce.300ms="search" 
                       class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Datum von</label>
                <input type="date" wire:model.live="dateFrom" 
                       class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Datum bis</label>
                <input type="date" wire:model.live="dateTo" 
                       class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Maskentyp</label>
                <select wire:model.live="selectedType" 
                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                    <option value="">Alle</option>
                    @foreach($types as $value => $label)
                        <option value="{{ $value }}">{{ $label }}</option>
                    @endforeach
                </select>
            </div>
        </div>
    </div>

    <!-- Main Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        @forelse($buchungen as $buchung)
            <div class="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300">
                <div class="p-6">
                    <!-- Header -->
                    <div class="flex justify-between items-start mb-4">
                        <div>
                            <span class="text-sm text-gray-500">
                                {{ \Carbon\Carbon::parse($buchung->created_at)->format('d.m.Y H:i') }}
                            </span>
                            <div class="text-lg font-semibold">#{{ $buchung->id }}</div>
                        </div>
                        <span @class([
                            'px-3 py-1 rounded-full text-sm font-medium',
                            'bg-blue-100 text-blue-800' => $buchung->type === 'strahlen',
                            'bg-red-100 text-red-800' => $buchung->type === 'putzen',
                            'bg-green-100 text-green-800' => $buchung->type === 'normal'
                        ])>
                            {{ strtoupper($buchung->type) }}
                        </span>
                    </div>

                    <!-- Details Grid -->
                    <div class="grid grid-cols-2 gap-4 mb-6">
                        <div class="text-sm">
                            <div class="text-gray-500 mb-1">Rückmeldenummer</div>
                            <div class="font-medium">{{ $buchung->rueckmeldenummer }}</div>
                        </div>
                        <div class="text-sm">
                            <div class="text-gray-500 mb-1">Losgröße</div>
                            <div class="font-medium">{{ $buchung->losgroesse }}</div>
                        </div>
                        <div class="text-sm">
                            <div class="text-gray-500 mb-1">Modellnest</div>
                            <div class="font-medium">{{ $buchung->modellnest }}</div>
                        </div>
                        <div class="text-sm">
                            <div class="text-gray-500 mb-1">Menge</div>
                            <div class="font-medium">{{ $buchung->total_menge }}</div>
                        </div>
                        <div class="text-sm col-span-2">
                            <div class="text-gray-500 mb-1">Fehlermerkmal</div>
                            <div class="font-medium">
                                {{ $buchung->fehlermerkmal->sap_code }} - 
                                {{ $buchung->fehlermerkmal->beschreibung }}
                            </div>
                            @php
                                $ausschussquote = $this->getAusschussquote($buchung->rueckmeldenummer, $buchung->losgroesse);
                            @endphp
                            <div class="mt-2">
                                <div class="text-gray-500">Ausschussquote</div>
                                <div class="text-left">
                                    <span class="text-2xl font-bold {{ $ausschussquote <= 5 ? 'text-green-600' : 'text-red-600' }}">
                                        {{ $ausschussquote }}<span class="text-lg">%</span>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="text-sm">
                            <div class="text-gray-500 mb-1">Fehlerort</div>
                            <div class="font-medium">{{ $buchung->fehlerort->name }}</div>
                        </div>
                    </div>

                    <!-- Image Preview -->
                    @php
                        $fotos = $this->getFotoArray($buchung->foto_path);
                        $fotoCount = count($fotos);
                    @endphp
                    @if($fotoCount > 0)
                        <div class="relative mb-4">
                            <div class="relative w-full h-48 cursor-pointer"
                                 @click="showLightbox = true; 
                                        currentImageIndex = 0;
                                        selectedBuchung = {
                                            fotos: {{ json_encode(array_map(fn($path) => Storage::url($path), $fotos)) }}
                                        };
                                        resetTransform();">
                                <img src="{{ Storage::url($fotos[0]) }}"
                                     alt="Vorschau"
                                     class="w-full h-full object-cover rounded-lg">
                                @if($fotoCount > 1)
                                    <div class="absolute -top-2 -right-2 bg-blue-600 text-white text-xs font-bold rounded-full h-6 w-6 flex items-center justify-center">
                                        {{ $fotoCount }}
                                    </div>
                                @endif
                            </div>
                        </div>
                    @else
                        <div class="w-full h-48 bg-gray-100 rounded-lg flex items-center justify-center mb-4">
                            <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                            </svg>
                        </div>
                    @endif

                    <!-- Actions -->
                    <div class="flex justify-end space-x-3">
                        @if($buchung->kommentare)
                            <button wire:click="showComment({{ json_encode($buchung->kommentare) }})"
                                    x-on:click="showComment = true"
                                    class="p-2 rounded-full hover:bg-gray-100 transition-colors">
                                <svg class="w-5 h-5 text-gray-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"/>
                                </svg>
                            </button>
                        @endif
                        <button wire:click="downloadPDF({{ $buchung->id }})"
                                class="p-2 rounded-full hover:bg-gray-100 transition-colors">
                            <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        @empty
            <div class="col-span-full text-center py-12 text-gray-500">
                Keine Einträge gefunden
            </div>
        @endforelse
    </div>

    <!-- Lightbox Modal -->
    <div x-show="showLightbox" 
         x-cloak
         class="fixed inset-0 z-50 bg-black flex items-center justify-center"
         @keydown.escape.window="closeLightbox()"
         @click.self="closeLightbox()">
        
        <!-- Close Button -->
        <button @click="closeLightbox()" 
                class="fixed top-4 right-4 bg-black bg-opacity-50 hover:bg-opacity-75 rounded-full p-2 text-white hover:text-gray-200 transition-all duration-200 z-50">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
            </svg>
        </button>

        <!-- Image Counter -->
        <div class="fixed top-4 left-4 bg-black bg-opacity-50 px-3 py-1.5 rounded-full text-white text-sm z-50">
            <span x-text="selectedBuchung ? `${currentImageIndex + 1} / ${selectedBuchung.fotos.length}` : ''"></span>
        </div>

        <!-- Main Image Container with Navigation -->
        <div class="relative w-full h-full flex items-center justify-center">
            <!-- Main Image -->
            <template x-if="selectedBuchung">
                <div class="relative flex items-center">
                    <!-- Previous Button -->
                    <template x-if="selectedBuchung.fotos.length > 1">
                        <button @click.stop="previousImage()"
                                :disabled="currentImageIndex === 0"
                                class="absolute -left-16 bg-black bg-opacity-50 p-2 rounded-full text-white hover:bg-opacity-75 transition-all z-50"
                                :class="{ 'opacity-50 cursor-not-allowed': currentImageIndex === 0 }">
                            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                            </svg>
                        </button>
                    </template>

                    <img :src="selectedBuchung.fotos[currentImageIndex]"
                         class="max-h-[90vh] max-w-[90vw] object-contain"
                         :style="`transform: scale(${scale}) rotate(${rotation}deg)`">

                    <!-- Next Button -->
                    <template x-if="selectedBuchung.fotos.length > 1">
                        <button @click.stop="nextImage()"
                                :disabled="currentImageIndex === (selectedBuchung.fotos.length - 1)"
                                class="absolute -right-16 bg-black bg-opacity-50 p-2 rounded-full text-white hover:bg-opacity-75 transition-all z-50"
                                :class="{ 'opacity-50 cursor-not-allowed': currentImageIndex === selectedBuchung.fotos.length - 1 }">
                            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                            </svg>
                        </button>
                    </template>
                </div>
            </template>
        </div>

        <!-- Controls -->
        <div class="fixed bottom-4 left-1/2 transform -translate-x-1/2 flex items-center gap-2 bg-black bg-opacity-50 px-3 py-2 rounded-full">
            <button @click.stop="scale = Math.max(0.5, scale - 0.1)" 
                    class="p-1.5 text-white hover:text-gray-200">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"/>
                </svg>
            </button>
            <button @click.stop="scale = 1; rotation = 0;" 
                    class="p-1.5 text-white hover:text-gray-200">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"/>
                </svg>
            </button>
            <button @click.stop="scale = Math.min(3, scale + 0.1)" 
                    class="p-1.5 text-white hover:text-gray-200">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
                </svg>
            </button>
        </div>
    </div>

    <!-- Comment Modal -->
    <div x-show="showComment" 
         x-cloak
         class="fixed inset-0 z-40 bg-black bg-opacity-50 flex items-center justify-center p-4"
         @click.self="showComment = false">
        <div class="bg-gray-100 rounded-lg shadow-xl inline-block transform transition-all">
            <!-- Header -->
            <div class="flex justify-between items-center px-4 py-3 border-b border-gray-200 bg-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Kommentar</h3>
                <button @click="showComment = false" 
                        class="p-1.5 hover:bg-gray-300 rounded-full transition-colors ml-4">
                    <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>
            <!-- Content -->
            <div class="p-4">
                <pre class="text-gray-800 whitespace-pre-wrap" style="min-width: 300px; max-width: 500px; font-family: inherit;">{{ $selectedComment }}</pre>
            </div>
        </div>
    </div>
</div>

