<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Dashboard') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Übersichtskarten -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                <!-- Gesamtmenge -->
                <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-blue-500 bg-opacity-75">
                            <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm text-gray-500">Gesamtmenge</p>
                            <p class="text-2xl font-semibold text-gray-800" id="gesamtMenge">-</p>
                        </div>
                    </div>
                </div>

                <!-- Aktive Modellnester -->
                <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-green-500 bg-opacity-75">
                            <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm text-gray-500">Aktive Modellnester</p>
                            <p class="text-2xl font-semibold text-gray-800" id="aktiveModellnester">-</p>
                        </div>
                    </div>
                </div>

                <!-- Fehlerorte -->
                <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-yellow-500 bg-opacity-75">
                            <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm text-gray-500">Fehlerorte</p>
                            <p class="text-2xl font-semibold text-gray-800" id="anzahlFehlerorte">-</p>
                        </div>
                    </div>
                </div>

                <!-- Fehlertypen -->
                <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-red-500 bg-opacity-75">
                            <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"/>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm text-gray-500">Fehlertypen</p>
                            <p class="text-2xl font-semibold text-gray-800" id="anzahlFehlertypen">-</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Hauptdiagramme -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                <!-- Fehlerverteilung nach Typ -->
                <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                    <h3 class="text-lg font-semibold mb-4">Fehlerverteilung nach Typ</h3>
                    <div style="height: 300px;">
                        <canvas id="fehlerTypChart"></canvas>
                    </div>
                </div>

                <!-- Top 5 Fehlerorte -->
                <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                    <h3 class="text-lg font-semibold mb-4">Top 5 Fehlerorte</h3>
                    <div style="height: 300px;">
                        <canvas id="topFehlerorteChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- Zusätzliche Diagramme -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Modellnest-Verteilung -->
                <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                    <h3 class="text-lg font-semibold mb-4">Modellnest-Verteilung</h3>
                    <div style="height: 250px;">
                        <canvas id="modellnestChart"></canvas>
                    </div>
                </div>

                <!-- Fehlercode-Verteilung -->
                <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                    <h3 class="text-lg font-semibold mb-4">Fehlercode-Verteilung</h3>
                    <div style="height: 250px;">
                        <canvas id="fehlercodeChart"></canvas>
                    </div>
                </div>

                <!-- Zeitliche Entwicklung -->
                <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                    <h3 class="text-lg font-semibold mb-4">Fehler pro Tag</h3>
                    <div style="height: 250px;">
                        <canvas id="zeitverlaufChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Chart.js einbinden -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns"></script>

    <script>
        // Daten vom Server
        const buchungen = @json($buchungen);

        // Übersichtszahlen berechnen
        const gesamtMenge = buchungen.reduce((sum, b) => sum + b.gesamt_menge, 0);
        const uniqueModellnester = new Set(buchungen.map(b => b.modellnest)).size;
        const uniqueFehlerorte = new Set(buchungen.map(b => b.fehlerort_name)).size;
        const uniqueFehlertypen = new Set(buchungen.map(b => b.type)).size;

        // Übersichtszahlen anzeigen
        document.getElementById('gesamtMenge').textContent = gesamtMenge;
        document.getElementById('aktiveModellnester').textContent = uniqueModellnester;
        document.getElementById('anzahlFehlerorte').textContent = uniqueFehlerorte;
        document.getElementById('anzahlFehlertypen').textContent = uniqueFehlertypen;

        // Fehlertyp-Verteilung
        const typData = buchungen.reduce((acc, b) => {
            acc[b.type] = (acc[b.type] || 0) + b.gesamt_menge;
            return acc;
        }, {});

        new Chart(document.getElementById('fehlerTypChart'), {
            type: 'pie',
            data: {
                labels: Object.keys(typData),
                datasets: [{
                    data: Object.values(typData),
                    backgroundColor: ['#3B82F6', '#EF4444'],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right'
                    }
                }
            }
        });

        // Top 5 Fehlerorte
        const fehlerortData = buchungen.reduce((acc, b) => {
            acc[b.fehlerort_name] = (acc[b.fehlerort_name] || 0) + b.gesamt_menge;
            return acc;
        }, {});

        const topFehlerorte = Object.entries(fehlerortData)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 5);

        new Chart(document.getElementById('topFehlerorteChart'), {
            type: 'bar',
            data: {
                labels: topFehlerorte.map(([name]) => name),
                datasets: [{
                    label: 'Anzahl Fehler',
                    data: topFehlerorte.map(([,count]) => count),
                    backgroundColor: '#60A5FA',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // Modellnest-Verteilung
        const modellnestData = buchungen.reduce((acc, b) => {
            acc[b.modellnest] = (acc[b.modellnest] || 0) + b.gesamt_menge;
            return acc;
        }, {});

        new Chart(document.getElementById('modellnestChart'), {
            type: 'doughnut',
            data: {
                labels: Object.keys(modellnestData),
                datasets: [{
                    data: Object.values(modellnestData),
                    backgroundColor: ['#34D399', '#F87171', '#FBBF24', '#60A5FA', '#A78BFA'],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            font: {
                                size: 11
                            }
                        }
                    }
                }
            }
        });

        // Fehlercode-Verteilung
        const codeData = buchungen.reduce((acc, b) => {
            acc[b.fehlermerkmal_code] = (acc[b.fehlermerkmal_code] || 0) + b.gesamt_menge;
            return acc;
        }, {});

        new Chart(document.getElementById('fehlercodeChart'), {
            type: 'polarArea',
            data: {
                labels: Object.keys(codeData),
                datasets: [{
                    data: Object.values(codeData),
                    backgroundColor: ['#34D399', '#F87171', '#FBBF24'],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            font: {
                                size: 11
                            }
                        }
                    }
                }
            }
        });

        // Zeitliche Entwicklung
        const zeitData = buchungen.reduce((acc, b) => {
            const date = b.created_at.split(' ')[0];
            acc[date] = (acc[date] || 0) + b.gesamt_menge;
            return acc;
        }, {});

        new Chart(document.getElementById('zeitverlaufChart'), {
            type: 'line',
            data: {
                labels: Object.keys(zeitData),
                datasets: [{
                    label: 'Fehler pro Tag',
                    data: Object.values(zeitData),
                    borderColor: '#60A5FA',
                    backgroundColor: '#60A5FA20',
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    </script>
</x-app-layout>
