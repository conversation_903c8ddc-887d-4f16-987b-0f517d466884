**phpmyadmin Abfrage**

**Übersicht aller Daten mit Key und Sortierung**  

SELECT 
    sap.rueckmeldenummer,
    sap.type,
    sap.modellnest,
    fo.fehlerkey_sap, -- <PERSON>hlerkey vor Fehlerort
    fo.name AS fehlerort_name,
    COALESCE(fp.beschreibung, fs.beschreibung) AS fehlermerkmal_beschreibung,
    COALESCE(fp.code, fs.code) AS fehlermerkmal_code, -- Code-Spalte hinzufügen
    COALESCE(fp.abkuerzung, fs.abkuerzung) AS fehlermerkmal_abkuerzung, -- Abkürzung hinzufügen
    SUM(sap.menge) AS gesamt_menge
FROM 
    ausschussbuchungen_sap AS sap
LEFT JOIN 
    fehlerort AS fo ON sap.fehlerort_id = fo.id
LEFT JOIN 
    fehlermerkmal_putzens AS fp ON sap.fehlermerkmal_type = 'App\\Models\\FehlermermalPutzen' AND sap.fehlermerkmal_id = fp.id
LEFT JOIN 
    fehlermerkmal_strahlens AS fs ON sap.fehlermerkmal_type = 'App\\Models\\FehlermermalStrahlen' AND sap.fehlermerkmal_id = fs.id
GROUP BY 
    sap.rueckmeldenummer, 
    sap.type, 
    sap.modellnest, 
    fo.fehlerkey_sap, 
    fo.name, 
    fehlermerkmal_beschreibung,
    fehlermerkmal_code, -- Code zur GROUP BY-Klausel hinzufügen
    fehlermerkmal_abkuerzung -- Abkürzung zur GROUP BY-Klausel hinzufügen
ORDER BY 
    fehlermerkmal_code ASC, -- Nach Code sortieren
    fehlermerkmal_abkuerzung ASC, -- Nach Abkürzung sortieren
    fehlermerkmal_beschreibung ASC; -- Nach Beschreibung sortieren


**JSON Übergabe an SAP**
**Strahlen**
6305-0010 ## SAP nummer für W-GIJ
6305-1000-01 ## Modellnestnummer übertragung
6305-2000-01 ## Fehlerort übertragung

**Putzen**
6306-0010 ## SAP nummer für W-GIJ
6306-1000-01 ## Modellnestnummer übertragung
6306-2000-01 ## Fehlerort übertragung

Kannst du JSON Export bitte so anpassen das wenn das 'code'=>6305.


