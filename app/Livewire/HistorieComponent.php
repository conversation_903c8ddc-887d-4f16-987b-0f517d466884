<?php

namespace App\Livewire;

use App\Models\AusschussbuchungSap;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Livewire\Component;
use Livewire\WithPagination;
use PDF;

class HistorieComponent extends Component
{
    use WithPagination;

    protected $paginationTheme = 'tailwind';

    public $showFilters = false;

    public $search = '';

    public $dateFrom = '';

    public $dateTo = '';

    public $selectedType = '';

    public $selectedModellnest = '';

    public $selectedComment = '';

    public $sortField = 'latest_created_at';

    public $sortDirection = 'desc';

    // Lightbox Properties
    public $showLightbox = false;

    public $currentImageIndex = 0;

    public $currentBuchung = null;

    public $currentImages = [];

    protected $listeners = [
        'keydown.escape' => 'closeLightbox',
        'keydown.arrow-left' => 'previousImage',
        'keydown.arrow-right' => 'nextImage',
        'keydown.delete' => 'confirmDeleteCurrentImage',
    ];

    protected $queryString = [
        'search' => ['except' => ''],
        'dateFrom' => ['except' => ''],
        'dateTo' => ['except' => ''],
        'selectedType' => ['except' => ''],
        'selectedModellnest' => ['except' => ''],
        'sortField' => ['except' => 'latest_created_at'],
        'sortDirection' => ['except' => 'desc'],
    ];

    public function mount()
    {
        $this->dateFrom = Carbon::today()->format('Y-m-d');
        $this->dateTo = Carbon::today()->format('Y-m-d');
    }

    public function toggleFilters()
    {
        $this->showFilters = !$this->showFilters;
    }

    public function resetFilters()
    {
        $this->search = '';
        $this->dateFrom = Carbon::today()->format('Y-m-d');
        $this->dateTo = Carbon::today()->format('Y-m-d');
        $this->selectedType = '';
        $this->selectedModellnest = '';
    }

    public function downloadPDF($buchungId)
    {
        try {
            Log::info('Downloading PDF for buchung', ['buchungId' => $buchungId]);
            
            // Get the current buchung
            $currentBuchung = AusschussbuchungSap::findOrFail($buchungId);
            
            // Get all buchungen with the same Rückmeldenummer
            $allBuchungen = AusschussbuchungSap::query()
                ->where('rueckmeldenummer', $currentBuchung->rueckmeldenummer)
                ->get();
            
            // Calculate total quantity for this Rückmeldenummer
            $totalMenge = $allBuchungen->sum('menge');
            
            // Get the Losgröße from any of the buchungen (they should all have the same value)
            $losgroesse = $allBuchungen->first()->losgroesse;
            
            // Calculate scrap rate (Ausschussquote)
            $ausschussquote = $losgroesse > 0 ? round(($totalMenge / $losgroesse) * 100, 2) : 0;
            
            $buchung = AusschussbuchungSap::query()
                ->select([
                    'id',
                    'rueckmeldenummer',
                    'modellnest',
                    'type',
                    'fehlermerkmal_id',
                    'fehlermerkmal_type',
                    'fehlerort_id',
                    'foto_path',
                    'losgroesse',
                    DB::raw('SUM(menge) as total_menge')
                ])
                ->where('id', $buchungId)
                ->groupBy('id', 'rueckmeldenummer', 'modellnest', 'type', 'fehlermerkmal_id', 'fehlermerkmal_type', 'fehlerort_id', 'foto_path', 'losgroesse')
                ->with(['fehlermerkmal', 'fehlerort'])
                ->firstOrFail();
            
            // Convert image paths to full storage paths
            $images = $this->getFotoArray($buchung->foto_path);
            $fullPathImages = array_map(function($path) {
                return storage_path('app/public/' . $path);
            }, $images);

            $data = [
                'buchung' => $buchung,
                'images' => $fullPathImages,
                'totalMenge' => $totalMenge,
                'losgroesse' => $losgroesse,
                'ausschussquote' => $ausschussquote
            ];

            Log::info('Generating PDF with data', [
                'buchung_id' => $buchung->id,
                'total_menge' => $totalMenge,
                'losgroesse' => $losgroesse,
                'ausschussquote' => $ausschussquote,
                'rueckmeldenummer' => $buchung->rueckmeldenummer,
                'image_paths' => $fullPathImages
            ]);

            $pdf = PDF::loadView('livewire.ergebnisbericht-pdf', $data);
            
            // Generate a unique filename
            $filename = 'bericht_' . $buchung->id . '_' . now()->format('YmdHis') . '.pdf';
            
            return response()->streamDownload(function() use ($pdf) {
                echo $pdf->output();
            }, $filename);

        } catch (\Exception $e) {
            Log::error('PDF generation failed', [
                'buchungId' => $buchungId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            $this->dispatch('notify', [
                'type' => 'error',
                'message' => 'Fehler bei der PDF-Erstellung: ' . $e->getMessage()
            ]);
        }
    }

    public function getGroupedBuchungenProperty()
    {
        try {
            Log::info('Starting getGroupedBuchungenProperty');

            $query = AusschussbuchungSap::query()
                ->select(
                    'id',
                    'rueckmeldenummer',
                    'losgroesse',
                    'modellnest',
                    'type',
                    'fehlermerkmal_id',
                    'fehlermerkmal_type',
                    'fehlerort_id',
                    DB::raw('SUM(menge) as total_menge'),
                    DB::raw('MAX(created_at) as latest_created_at'),
                    'foto_path',
                    DB::raw('GROUP_CONCAT(DISTINCT kommentar) as kommentare')
                )
                ->with(['fehlermerkmal', 'fehlerort']);

            if ($this->search) {
                $search = '%'.$this->search.'%';
                $query->where(function ($q) use ($search) {
                    $q->where('rueckmeldenummer', 'like', $search)
                        ->orWhere('modellnest', 'like', $search)
                        ->orWhere('losgroesse', 'like', $search)
                        ->orWhereHas('fehlermerkmal', function ($q) use ($search) {
                            $q->where('beschreibung', 'like', $search)
                                ->orWhere('abkuerzung', 'like', $search)
                                ->orWhere('code', 'like', $search);
                        })
                        ->orWhereHas('fehlerort', function ($q) use ($search) {
                            $q->where('name', 'like', $search);
                        });
                });
            }

            if ($this->dateFrom) {
                $query->whereDate('created_at', '>=', $this->dateFrom);
            }

            if ($this->dateTo) {
                $query->whereDate('created_at', '<=', $this->dateTo);
            }

            if ($this->selectedType !== '') {
                $query->where('type', $this->selectedType);
            }

            if ($this->selectedModellnest !== '') {
                $query->where('modellnest', $this->selectedModellnest);
            }

            $result = $query
                ->groupBy('id', 'rueckmeldenummer', 'losgroesse', 'modellnest', 'type', 'fehlermerkmal_id', 'fehlermerkmal_type', 'fehlerort_id', 'foto_path')
                ->orderBy($this->sortField, $this->sortDirection)
                ->get();

            return $result;
        } catch (\Exception $e) {
            Log::error('Error in getGroupedBuchungenProperty', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    public function getFotoArray($fotoPaths)
    {
        try {
            if (empty($fotoPaths)) {
                return [];
            }

            if (is_array($fotoPaths)) {
                $paths = $fotoPaths;
            } else {
                $fotoPaths = str_replace('\\/', '/', $fotoPaths);
                $fotoPaths = str_replace('\\\\', '\\', $fotoPaths);
                $paths = json_decode($fotoPaths, true);
                
                if (!is_array($paths)) {
                    $fotoPaths = str_replace(['[', ']', '"', '\\'], '', $fotoPaths);
                    $paths = explode(',', $fotoPaths);
                }
            }
            
            return array_values(array_filter(array_map(function ($path) {
                $path = trim($path);
                if (empty($path)) return null;

                $filename = basename($path);
                $path = 'fotos/' . $filename;

                if (!Storage::disk('public')->exists($path)) {
                    return null;
                }

                return $path;
            }, $paths)));
        } catch (\Exception $e) {
            Log::error('Error in getFotoArray', [
                'fotoPaths' => $fotoPaths,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    public function render()
    {
        return view('livewire.historie-component', [
            'buchungen' => $this->groupedBuchungen,
            'modellnests' => ['M1', 'M2', 'M3', 'M4', 'M5', 'M6'],
            'types' => ['normal' => 'Normal', 'strahlen' => 'Strahlen', 'putzen' => 'Putzen'],
        ]);
    }

    public function openLightbox($buchungId, $initialImageIndex = 0)
    {
        try {
            Log::info('Opening lightbox', ['buchungId' => $buchungId, 'initialImageIndex' => $initialImageIndex]);

            $buchung = AusschussbuchungSap::find($buchungId);
            if (!$buchung) {
                Log::error('Buchung not found', ['buchungId' => $buchungId]);
                $this->dispatch('notify', [
                    'type' => 'error',
                    'message' => 'Buchung nicht gefunden.'
                ]);
                return;
            }

            $this->currentBuchung = $buchung;
            $this->currentImages = $this->getFotoArray($buchung->foto_path);

            if (empty($this->currentImages)) {
                Log::warning('No images found for buchung', ['buchungId' => $buchungId]);
                $this->dispatch('notify', [
                    'type' => 'warning',
                    'message' => 'Keine Bilder gefunden.'
                ]);
                return;
            }

            $this->currentImageIndex = min($initialImageIndex, count($this->currentImages) - 1);
            $this->showLightbox = true;
        } catch (\Exception $e) {
            Log::error('Error opening lightbox', [
                'buchungId' => $buchungId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            $this->dispatch('notify', [
                'type' => 'error',
                'message' => 'Fehler beim Öffnen des Bildes.'
            ]);
        }
    }

    public function closeLightbox()
    {
        $this->showLightbox = false;
        $this->currentImageIndex = 0;
        $this->currentBuchung = null;
        $this->currentImages = [];
    }

    public function nextImage()
    {
        if (!empty($this->currentImages)) {
            $this->currentImageIndex = ($this->currentImageIndex + 1) % count($this->currentImages);
        }
    }

    public function previousImage()
    {
        if (!empty($this->currentImages)) {
            $this->currentImageIndex = ($this->currentImageIndex - 1 + count($this->currentImages)) % count($this->currentImages);
        }
    }

    public function confirmDeleteCurrentImage()
    {
        if (!empty($this->currentImages) && isset($this->currentImages[$this->currentImageIndex])) {
            $this->dispatch('confirm-delete', [
                'title' => 'Bild löschen',
                'message' => 'Möchten Sie dieses Bild wirklich löschen?',
                'imageIndex' => $this->currentImageIndex,
                'buchungId' => $this->currentBuchung->id,
            ]);
        }
    }

    public function deleteImage($imageIndex)
    {
        try {
            if (!$this->currentBuchung) {
                $this->dispatch('notify', [
                    'type' => 'error',
                    'message' => 'Keine aktuelle Buchung gefunden.'
                ]);
                return;
            }

            if (!isset($this->currentImages[$imageIndex])) {
                $this->dispatch('notify', [
                    'type' => 'error',
                    'message' => 'Ungültiger Bildindex.'
                ]);
                return;
            }

            $imagePath = $this->currentImages[$imageIndex];

            if (Storage::exists($imagePath)) {
                Storage::delete($imagePath);
            }

            $remainingImages = array_values(array_filter($this->currentImages, function ($path, $index) use ($imageIndex) {
                return $index !== $imageIndex;
            }, ARRAY_FILTER_USE_BOTH));

            $this->currentBuchung->foto_path = json_encode($remainingImages);
            $this->currentBuchung->save();

            $this->currentImages = $remainingImages;
            if (empty($this->currentImages)) {
                $this->closeLightbox();
            } else {
                $this->currentImageIndex = min($this->currentImageIndex, count($this->currentImages) - 1);
            }

            $this->dispatch('notify', [
                'type' => 'success',
                'message' => 'Bild wurde erfolgreich gelöscht.'
            ]);
        } catch (\Exception $e) {
            Log::error('Error deleting image', [
                'imageIndex' => $imageIndex,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            $this->dispatch('notify', [
                'type' => 'error',
                'message' => 'Fehler beim Löschen des Bildes: ' . $e->getMessage()
            ]);
        }
    }

    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortField = $field;
            $this->sortDirection = 'asc';
        }
    }

    public function getAusschussquote($rueckmeldenummer, $losgroesse)
    {
        $total_menge = DB::table('ausschussbuchungen_sap')
            ->where('rueckmeldenummer', $rueckmeldenummer)
            ->sum('menge');

        return $losgroesse > 0 ? round(($total_menge / $losgroesse) * 100, 1) : 0;
    }

    public function showComment($comment)
    {
        $this->selectedComment = str_replace('\n', "\n", html_entity_decode($comment));
    }
}
