<?php

namespace App\Livewire;

use Livewire\Component;
use Illuminate\Support\Facades\Session;

class ConfigPasswordComponent extends Component
{
    public $password = '';
    public $error = '';

    public function checkPassword()
    {
        // Das Passwort sollte in der .env-<PERSON>i oder in der Datenbank gespeichert sein
        // Hier verwenden wir ein hartcodiertes Passwort zu Demonstrationszwecken (Arif)
        if ($this->password === 'yadeyade') {
            Session::put('config_access', true);
            return redirect()->route('konfiguration');
        }

        $this->error = 'Falsches Passwort';
        $this->password = '';
    }

    public function render()
    {
        return view('livewire.config-password-component');
    }
}
