<?php

namespace App\Livewire;

use App\Models\AusschussbuchungSap;
use App\Models\FehlermermalNormal;
use App\Models\FehlermermalPutzen;
use App\Models\FehlermermalStrahlen;
use App\Models\Fehlerort;
use App\Models\Kostenstelle;
use App\Models\Ursache;
use App\Models\Vorgang;
use App\Models\VorlaeufigeBuchung;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Livewire\Component;
use Livewire\WithFileUploads;

class FehlerErfassungForm extends Component
{
    use WithFileUploads;

    // Add tempPhoto to the list of uploadable properties
    protected $uploads = ['fotos', 'tempPhoto'];

    // Form fields
    public $rueckmeldenummer = '';

    public $losgroesse = null;

    public $selectedModellnest = null;

    public $selectedFehlermerkmal = null;

    public $selectedFehlerort = null;

    public $menge = 0;

    public $kommentar = '';

    public $fotos = [];

    // Temporary photo property for iOS uploads
    public $tempPhoto = null;

    public $type = 'strahlen'; // Default to strahlen

    public $showTypeSelection = true;

    public $recentRueckmeldenummern = [];

    public $isRueckmeldenummerLocked = false;

    public $showKommentar = false;

    public $showFotoUpload = false;

    public $showAllModellnests = false;

    public $modellnestBlocks = [];

    public $currentBlock = 0;

    // Neue Felder für NORMAL-Maske
    public $vorgang = '';

    public $selectedKostenstelle = null;

    public $ursache = '';

    // Lists for dropdowns
    public $fehlermerkmale = [];

    public $fehlerorte = [];

    public $kostenstellen = [];

    public $vorgaenge = [];

    public $ursachen = [];

    protected $messages = [
        'fotos.*.max' => 'Jedes Foto darf maximal 10MB groß sein.',
        'fotos.*.image' => 'Die ausgewählten Dateien müssen Bilder sein.',
        'rueckmeldenummer.required' => 'Bitte geben Sie eine Rückmeldenummer ein.',
        'selectedModellnest.required' => 'Wähle bitte ein Modellnest aus.',
        'selectedFehlermerkmal.required' => 'Wähle bitte ein Fehlermerkmal aus.',
        'selectedFehlerort.required' => 'Wähle bitte ein Fehlerort aus.',
        'menge.required' => 'Gib bitte eine Menge ein.',
        'menge.min' => 'Die Menge muss mindestens 1 sein.',
        'vorgang.required_if' => 'Bitte geben Sie einen Vorgang ein.',
        'selectedKostenstelle.required_if' => 'Bitte wählen Sie eine Kostenstelle aus.',
        'ursache.required_if' => 'Bitte geben Sie eine Ursache ein.',
    ];

    public function mount()
    {
        $this->loadFehlerorte();
        $this->loadFehlermerkmale();
        $this->loadRecentRueckmeldenummern();
        $this->initializeModellnests();
        $this->loadKostenstellen();
        $this->loadVorgaenge();
        $this->loadUrsachen();
    }

    public function initializeModellnests()
    {
        $allNests = array_map(function ($i) {
            return 'M'.$i;
        }, range(1, 42));

        // Teile die Modellneste in 6er-Blöcke
        $this->modellnestBlocks = array_chunk($allNests, 6);
    }

    public function toggleAllModellnests()
    {
        $this->showAllModellnests = ! $this->showAllModellnests;
    }

    public function selectBlock($blockIndex)
    {
        $this->currentBlock = $blockIndex;
        $this->showAllModellnests = false;
    }

    public function loadRecentRueckmeldenummern()
    {
        // Hole alle vorläufigen Buchungen (ohne Limit)
        $vorlaeufigeBuchungen = DB::table('vorlaeufige_ausschussbuchungen')
            ->select('rueckmeldenummer')
            ->selectRaw('MAX(created_at) as last_created')
            ->selectRaw("'vorläufig' as status")
            ->selectRaw('MAX(type) as type')
            ->whereNull('deleted_at')
            ->when($this->type === 'normal', function ($query) {
                return $query->where('type', 'normal')
                    ->groupBy('rueckmeldenummer');
            })
            ->when($this->type !== 'normal', function ($query) {
                return $query->whereIn('type', ['strahlen', 'putzen'])
                    ->groupBy('rueckmeldenummer');
            })
            ->get()
            ->map(function ($item) {
                return [
                    'rueckmeldenummer' => $item->rueckmeldenummer,
                    'last_created' => $item->last_created,
                    'status' => $item->status,
                    'type' => $item->type,
                ];
            });

        // Hole die letzten 10 abgesendeten Buchungen
        $ausschussBuchungen = DB::table('ausschussbuchungen_sap')
            ->select('rueckmeldenummer')
            ->selectRaw('MAX(created_at) as last_created')
            ->selectRaw("'abgesendet' as status")
            ->selectRaw('MAX(type) as type')
            ->whereNull('deleted_at')
            ->when($this->type === 'normal', function ($query) {
                return $query->where('type', 'normal')
                    ->groupBy('rueckmeldenummer');
            })
            ->when($this->type !== 'normal', function ($query) {
                return $query->whereIn('type', ['strahlen', 'putzen'])
                    ->groupBy('rueckmeldenummer');
            })
            ->orderBy('last_created', 'desc')
            ->limit(10)
            ->get()
            ->map(function ($item) {
                return [
                    'rueckmeldenummer' => $item->rueckmeldenummer,
                    'last_created' => $item->last_created,
                    'status' => $item->status,
                    'type' => $item->type,
                ];
            });

        // Kombiniere die Ergebnisse
        $alleBuchungen = $vorlaeufigeBuchungen->concat($ausschussBuchungen)
            ->sortByDesc('last_created')
            ->unique('rueckmeldenummer')
            ->values()
            ->all();

        $this->recentRueckmeldenummern = $alleBuchungen;
    }

    public function loadFehlerorte()
    {
        if ($this->type === 'normal') {
            // Für die NORMAL-Maske laden wir direkt die Kostenstellen
            $this->fehlerorte = Kostenstelle::select('id', 'kostenstelle', 'bezeichnung')
                ->orderBy('kostenstelle')
                ->get()
                ->map(function ($kostenstelle) {
                    return [
                        'id' => $kostenstelle->id,
                        'name' => $kostenstelle->kostenstelle.' - '.$kostenstelle->bezeichnung,
                    ];
                })
                ->toArray();
        } else {
            // Für STRAHLEN und PUTZEN laden wir die Fehlerorte, sortiert nach fehlerkey_sap
            $this->fehlerorte = Fehlerort::orderBy('fehlerkey_sap', 'asc')
                ->get()
                ->map(function ($ort) {
                    return [
                        'id' => $ort->id,
                        'name' => $ort->fehlerkey_sap . ' - ' . $ort->name
                    ];
                })
                ->toArray();
        }
    }

    public function setType($type)
    {
        $oldType = $this->type;
        $this->type = $type;
        $this->showTypeSelection = false;

        // Überprüfe, ob die aktuelle Rückmeldenummer für den neuen Typ existiert
        if ($this->rueckmeldenummer) {
            $existsInNewType = VorlaeufigeBuchung::where('rueckmeldenummer', $this->rueckmeldenummer)
                ->where('type', $type)
                ->exists();

            $existsInOldType = VorlaeufigeBuchung::where('rueckmeldenummer', $this->rueckmeldenummer)
                ->where('type', $oldType)
                ->exists();

            // Wenn die Nummer im alten Typ existiert aber nicht im neuen,
            // oder wenn sie im neuen Typ existiert aber nicht im alten,
            // dann setze die Nummer zurück
            if (($existsInOldType && ! $existsInNewType) || (! $existsInOldType && $existsInNewType)) {
                $this->reset([
                    'rueckmeldenummer',
                    'selectedModellnest',
                    'selectedFehlermerkmal',
                    'selectedFehlerort',
                    'menge',
                    'kommentar',
                    'fotos',
                    'isRueckmeldenummerLocked',
                ]);
            }
        }

        $this->loadFehlermerkmale();
        $this->loadRecentRueckmeldenummern();
    }

    public function loadFehlermerkmale()
    {
        if ($this->type === 'strahlen') {
            $this->fehlermerkmale = FehlermermalStrahlen::where('code', '6305')
                ->orderBy('beschreibung')
                ->get();
        } elseif ($this->type === 'putzen') {
            $this->fehlermerkmale = FehlermermalPutzen::where('code', '6306')
                ->orderBy('beschreibung')
                ->get();
        } elseif ($this->type === 'normal') {
            $this->fehlermerkmale = FehlermermalNormal::orderBy('sap_code')
                ->get();
        }
    }

    public function incrementMenge()
    {
        $this->menge++;
    }

    public function decrementMenge()
    {
        if ($this->menge > 0) {
            $this->menge--;
        }
    }

    public function removeFoto($index)
    {
        // Remove the photo at the specified index
        unset($this->fotos[$index]);
        
        // Re-index the array to maintain sequential keys
        $this->fotos = array_values($this->fotos);
        
        // If no photos left, hide the photo upload section
        if (empty($this->fotos)) {
            $this->showFotoUpload = false;
        }
    }

    /**
     * Add a photo from the temporary upload to the fotos array
     */
    public function addPhotoFromTemp()
    {
        if (!empty($this->tempPhoto)) {
            // Add the temporary photo to the fotos array
            $this->fotos[] = $this->tempPhoto;
            
            // Clear the temporary photo
            $this->tempPhoto = null;
            
            // Make sure the photo preview is shown
            $this->showFotoUpload = true;
        }
    }

    public function save()
    {
        $validationRules = [
            'rueckmeldenummer' => 'required',
            'selectedFehlermerkmal' => 'required',
            'menge' => 'required|integer|min:1',
            'losgroesse' => 'nullable|integer|min:1|max:9999',
            'fotos.*' => 'nullable|image|max:10240', // max 10MB pro Foto
            'tempPhoto' => 'nullable|image|max:10240', // max 10MB pro Foto
        ];

        // Zusätzliche Validierung für NORMAL-Maske
        if ($this->type === 'normal') {
            $validationRules['vorgang'] = 'required';
            $validationRules['selectedKostenstelle'] = 'required';
            $validationRules['ursache'] = 'required';
        } else {
            $validationRules['selectedModellnest'] = 'required';
            $validationRules['selectedFehlerort'] = 'required';
        }

        $this->validate($validationRules);

        $fotoPaths = [];
        if (! empty($this->fotos)) {
            foreach ($this->fotos as $foto) {
                $fotoPaths[] = $foto->store('fotos', 'public');
            }
        }

        $fehlermerkmalType = match ($this->type) {
            'strahlen' => FehlermermalStrahlen::class,
            'putzen' => FehlermermalPutzen::class,
            'normal' => FehlermermalNormal::class,
            default => throw new \Exception('Ungültiger Typ'),
        };

        // Set default value for modellnest in Normal mask if none selected
        $modellnest = $this->selectedModellnest;
        if ($this->type === 'normal' && empty($modellnest)) {
            $modellnest = 'OHNE';
        }

        // Handle Fehlerort
        if ($this->type === 'normal') {
            // Erstelle oder hole den Standard-Fehlerort für normale Buchungen
            $fehlerort = Fehlerort::firstOrCreate(
                ['name' => 'Standard'],
                [
                    'name' => 'Standard',
                    'fehlerkey_sap' => '00',
                ]
            );
            $fehlerortId = $fehlerort->id;
        } else {
            $fehlerortId = $this->selectedFehlerort;
        }

        // Erstelle die Buchung
        $buchungsDaten = [
            'rueckmeldenummer' => $this->rueckmeldenummer,
            'losgroesse' => $this->losgroesse,
            'modellnest' => $modellnest,
            'fehlermerkmal_id' => $this->selectedFehlermerkmal,
            'fehlermerkmal_type' => $fehlermerkmalType,
            'fehlerort_id' => $fehlerortId,
            'menge' => $this->menge,
            'kommentar' => $this->kommentar,
            'foto_path' => json_encode($fotoPaths),
            'type' => $this->type,
        ];

        // Füge die NORMAL-Maske spezifischen Felder hinzu
        if ($this->type === 'normal') {
            $buchungsDaten['vorgang'] = $this->vorgang;
            $buchungsDaten['kostenstelle_id'] = $this->selectedKostenstelle;
            $buchungsDaten['ursache'] = $this->ursache;
        }

        VorlaeufigeBuchung::create($buchungsDaten);

        $this->isRueckmeldenummerLocked = true;
        $this->reset([
            'selectedFehlermerkmal',
            'selectedFehlerort',
            'menge',
            'kommentar',
            'fotos',
            'vorgang',
            'selectedKostenstelle',
            'ursache',
        ]);

        // Aktualisiere die Liste der Rückmeldenummern
        $this->loadRecentRueckmeldenummern();

        $this->dispatch('fehler-saved');
    }

    public function resetForm()
    {
        $this->reset([
            'rueckmeldenummer',
            'losgroesse',
            'selectedModellnest',
            'selectedFehlermerkmal',
            'selectedFehlerort',
            'menge',
            'kommentar',
            'fotos',
            'isRueckmeldenummerLocked',
            'showTypeSelection',
            'showKommentar',
            'showFotoUpload',
            'vorgang',
            'selectedKostenstelle',
            'ursache'
        ]);
        $this->loadRecentRueckmeldenummern();
    }

    public function submit()
    {
        // Hole alle vorläufigen Buchungen für diese Rückmeldenummer
        $vorlaeufigeBuchungen = VorlaeufigeBuchung::where('rueckmeldenummer', $this->rueckmeldenummer)
            ->get();

        // Übertrage alle vorläufigen Buchungen in die endgültige Tabelle
        foreach ($vorlaeufigeBuchungen as $buchung) {
            AusschussbuchungSap::create($buchung->toAusschussbuchung());
        }

        // Lösche die vorläufigen Buchungen
        VorlaeufigeBuchung::where('rueckmeldenummer', $this->rueckmeldenummer)->delete();

        // Aktualisiere die Liste der letzten Rückmeldenummern
        $this->loadRecentRueckmeldenummern();

        // Setze das Formular zurück
        $this->reset([
            'rueckmeldenummer',
            'losgroesse',
            'selectedModellnest',
            'selectedFehlermerkmal',
            'selectedFehlerort',
            'menge',
            'kommentar',
            'fotos',
            'isRueckmeldenummerLocked',
        ]);

        $this->dispatch('fehler-submitted');
    }

    public function setRueckmeldenummer($nummer)
    {
        // Wenn die gleiche Nummer nochmal geklickt wird, entsperren
        if ($this->rueckmeldenummer === $nummer) {
            $this->rueckmeldenummer = '';
            $this->losgroesse = null;
            $this->isRueckmeldenummerLocked = false;
            return;
        }

        // Prüfe ob es bereits eine vorläufige Buchung mit dieser Nummer gibt
        $existingBuchung = VorlaeufigeBuchung::where('rueckmeldenummer', $nummer)
            ->first();

        if ($existingBuchung && $existingBuchung->losgroesse) {
            $this->losgroesse = $existingBuchung->losgroesse;
        } else {
            // Wenn keine vorläufige Buchung gefunden wurde, suche in den bereits gespeicherten Buchungen
            $savedBuchung = AusschussbuchungSap::where('rueckmeldenummer', $nummer)
                ->whereNotNull('losgroesse')
                ->first();
            
            if ($savedBuchung) {
                $this->losgroesse = $savedBuchung->losgroesse;
            }
        }

        // Neue Nummer setzen
        $this->rueckmeldenummer = $nummer;
        $this->isRueckmeldenummerLocked = true;

        // Lade die vorherigen Buchungen aus der ausschussbuchungen_sap Tabelle
        $buchungen = AusschussbuchungSap::where('rueckmeldenummer', $nummer)
            ->with(['fehlermerkmal', 'fehlerort'])
            ->orderBy('created_at', 'desc')
            ->get();

        // Zeige die Buchungen im View an
        $this->dispatch('show-previous-buchungen', buchungen: $buchungen);
    }

    public function render()
    {
        $vorlaeufigeBuchungen = [];
        $letzteBuchungen = [];

        if ($this->rueckmeldenummer) {
            $vorlaeufigeBuchungen = VorlaeufigeBuchung::where('rueckmeldenummer', $this->rueckmeldenummer)
                ->with(['fehlermerkmal', 'fehlerort'])
                ->get();

            $letzteBuchungen = AusschussbuchungSap::where('rueckmeldenummer', $this->rueckmeldenummer)
                ->with(['fehlermerkmal', 'fehlerort'])
                ->orderBy('created_at', 'desc')
                ->get();
        }

        return view('livewire.fehler-erfassung-form', [
            'vorlaeufigeBuchungen' => $vorlaeufigeBuchungen,
            'letzteBuchungen' => $letzteBuchungen,
        ]);
    }

    public function deleteVorlaeufigeBuchung($id)
    {
        $buchung = VorlaeufigeBuchung::find($id);

        if ($buchung) {
            // Lösche die Fotos aus dem Storage
            if ($buchung->foto_path) {
                $fotoPaths = json_decode($buchung->foto_path, true);
                if (is_array($fotoPaths)) {
                    foreach ($fotoPaths as $path) {
                        if (Storage::exists('public/'.$path)) {
                            Storage::delete('public/'.$path);
                        }
                    }
                }
            }

            // Lösche den Eintrag
            $buchung->delete();
        }

        // Wenn keine vorläufigen Buchungen mehr für diese Rückmeldenummer existieren
        if (VorlaeufigeBuchung::where('rueckmeldenummer', $this->rueckmeldenummer)->count() === 0) {
            $this->reset([
                'rueckmeldenummer',
                'selectedModellnest',
                'selectedFehlermerkmal',
                'selectedFehlerort',
                'menge',
                'kommentar',
                'fotos',
                'isRueckmeldenummerLocked',
            ]);
        }

        // Aktualisiere die Liste der Rückmeldenummern
        $this->loadRecentRueckmeldenummern();
    }

    public function toggleKommentar()
    {
        $this->showKommentar = ! $this->showKommentar;
    }

    public function toggleFotoUpload()
    {
        $this->showFotoUpload = ! $this->showFotoUpload;
    }

    public function deleteRueckmeldenummer($nummer)
    {
        // Lösche alle vorläufigen Buchungen für diese Rückmeldenummer
        $buchungen = VorlaeufigeBuchung::where('rueckmeldenummer', $nummer)->get();

        foreach ($buchungen as $buchung) {
            // Lösche die Fotos aus dem Storage
            if ($buchung->foto_path) {
                $fotoPaths = json_decode($buchung->foto_path, true);
                if (is_array($fotoPaths)) {
                    foreach ($fotoPaths as $path) {
                        if (Storage::exists('public/'.$path)) {
                            Storage::delete('public/'.$path);
                        }
                    }
                }
            }

            // Lösche den Eintrag
            $buchung->delete();
        }

        // Wenn die aktuelle Rückmeldenummer gelöscht wurde, setze das Formular zurück
        if ($this->rueckmeldenummer === $nummer) {
            $this->reset([
                'rueckmeldenummer',
                'losgroesse',
                'selectedModellnest',
                'selectedFehlermerkmal',
                'selectedFehlerort',
                'menge',
                'kommentar',
                'fotos',
                'isRueckmeldenummerLocked',
            ]);
        }

        // Aktualisiere die Liste der Rückmeldenummern
        $this->loadRecentRueckmeldenummern();
    }

    public function loadKostenstellen()
    {
        $this->kostenstellen = Kostenstelle::orderBy('kostenstelle')->get();
    }

    public function loadVorgaenge()
    {
        $this->vorgaenge = Vorgang::orderBy('code')->get();
    }

    public function loadUrsachen()
    {
        $this->ursachen = Ursache::orderBy('kategorie')
            ->orderBy('code')
            ->get();
    }

    public function saveFehler()
    {
        // Validiere die Eingaben
        $this->validate([
            'rueckmeldenummer' => 'required',
            'selectedModellnest' => 'required',
            'selectedFehlermerkmal' => 'required',
            'selectedFehlerort' => $this->type !== 'normal' ? 'required' : '',
            'menge' => 'required|numeric|min:1',
            'losgroesse' => 'nullable|numeric|min:1',
        ]);

        // Speichere die Fotos
        $fotoPaths = [];
        if (count($this->fotos) > 0) {
            foreach ($this->fotos as $foto) {
                $path = $foto->store('fotos', 'public');
                $fotoPaths[] = $path;
            }
        }

        // Erstelle die vorläufige Buchung
        VorlaeufigeBuchung::create([
            'rueckmeldenummer' => $this->rueckmeldenummer,
            'losgroesse' => $this->losgroesse,
            'modellnest' => $this->selectedModellnest,
            'fehlermerkmal_type' => $this->type === 'normal' ? 'App\\Models\\FehlermerkalNormal' : ($this->type === 'strahlen' ? 'App\\Models\\FehlermerkalStrahlen' : 'App\\Models\\FehlermerkalPutzen'),
            'fehlermerkmal_id' => $this->selectedFehlermerkmal,
            'fehlerort_id' => $this->selectedFehlerort,
            'menge' => $this->menge,
            'kommentar' => $this->kommentar,
            'foto_path' => count($fotoPaths) > 0 ? json_encode($fotoPaths) : null,
            'type' => $this->type,
            'vorgang' => $this->vorgang,
            'kostenstelle_id' => $this->selectedKostenstelle,
            'ursache' => $this->ursache,
        ]);

        // Sperre die Rückmeldenummer
        $this->isRueckmeldenummerLocked = true;

        // Setze die Formularfelder zurück, außer die Rückmeldenummer
        $this->reset([
            'selectedModellnest',
            'selectedFehlermerkmal',
            'selectedFehlerort',
            'menge',
            'kommentar',
            'fotos',
        ]);

        // Aktualisiere die Liste der Rückmeldenummern
        $this->loadRecentRueckmeldenummern();

        $this->dispatch('fehler-saved');
    }

    public function updatedFotos($value, $key)
    {
        // Show photo preview when any photos are added
        if (!empty($this->fotos)) {
            $this->showFotoUpload = true;
        }
    }
    
    /**
     * Handle when a temporary photo is updated
     */
    public function updatedTempPhoto()
    {
        // This is handled by the addPhotoFromTemp method
    }
}
