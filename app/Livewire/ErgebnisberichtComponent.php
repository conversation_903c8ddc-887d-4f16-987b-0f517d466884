<?php

namespace App\Livewire;

use App\Models\AusschussbuchungSap;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use Livewire\Component;

class ErgebnisberichtComponent extends Component
{
    public $buchung;
    public $showEmailModal = false;
    public $emailAddress = '';
    public $pdfPath = null;

    protected $rules = [
        'emailAddress' => 'required|email',
    ];

    public function mount($buchungId)
    {
        $this->buchung = AusschussbuchungSap::with(['fehlermerkmal', 'fehlerort'])->findOrFail($buchungId);
    }

    public function generatePDF()
    {
        try {
            $data = [
                'buchung' => $this->buchung,
                'images' => $this->getProcessedImages(),
            ];

            $pdf = PDF::loadView('livewire.ergebnisbericht-pdf', $data);
            
            // Generate a unique filename
            $filename = 'bericht_' . $this->buchung->id . '_' . now()->format('YmdHis') . '.pdf';
            $path = 'public/berichte/' . $filename;
            
            // Save PDF to storage
            Storage::put($path, $pdf->output());
            
            $this->pdfPath = $path;
            
            $this->dispatch('notify', [
                'type' => 'success',
                'message' => 'PDF wurde erfolgreich erstellt.'
            ]);

            return Storage::url($path);
        } catch (\Exception $e) {
            Log::error('PDF generation failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            $this->dispatch('notify', [
                'type' => 'error',
                'message' => 'Fehler bei der PDF-Erstellung: ' . $e->getMessage()
            ]);
        }
    }

    public function downloadPDF()
    {
        if (!$this->pdfPath) {
            $this->generatePDF();
        }

        return Storage::download($this->pdfPath);
    }

    public function openEmailModal()
    {
        if (!$this->pdfPath) {
            $this->generatePDF();
        }
        $this->showEmailModal = true;
    }

    public function sendEmail()
    {
        $this->validate();

        try {
            Mail::send([], [], function ($message) {
                $message->to($this->emailAddress)
                    ->subject('Ergebnisbericht #' . $this->buchung->id)
                    ->attach(Storage::path($this->pdfPath));
            });

            $this->showEmailModal = false;
            $this->emailAddress = '';

            $this->dispatch('notify', [
                'type' => 'success',
                'message' => 'Bericht wurde erfolgreich per E-Mail versendet.'
            ]);
        } catch (\Exception $e) {
            Log::error('Email sending failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            $this->dispatch('notify', [
                'type' => 'error',
                'message' => 'Fehler beim E-Mail-Versand: ' . $e->getMessage()
            ]);
        }
    }

    private function getProcessedImages()
    {
        $fotoPaths = $this->buchung->foto_path;
        if (empty($fotoPaths)) {
            return [];
        }

        try {
            if (is_string($fotoPaths)) {
                $fotoPaths = json_decode($fotoPaths, true);
            }

            return array_filter(array_map(function ($path) {
                $path = trim($path);
                if (empty($path)) return null;

                $filename = basename($path);
                $path = 'fotos/' . $filename;

                if (!Storage::disk('public')->exists($path)) {
                    return null;
                }

                return Storage::disk('public')->path($path);
            }, $fotoPaths));
        } catch (\Exception $e) {
            Log::error('Error processing images for PDF', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return [];
        }
    }

    public function render()
    {
        return view('livewire.ergebnisbericht-component');
    }
} 