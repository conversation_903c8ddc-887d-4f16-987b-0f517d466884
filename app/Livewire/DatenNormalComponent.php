<?php

namespace App\Livewire;

use App\Models\AusschussbuchungSap;
use Livewire\Component;
use Illuminate\Support\Facades\DB;

class DatenNormalComponent extends Component
{
    public $sortField = 'rueckmeldenummer';
    public $sortDirection = 'desc';

    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortField = $field;
            $this->sortDirection = 'asc';
        }
    }

    public function getBuchungenProperty()
    {
        $query = AusschussbuchungSap::with([
            'fehlermerkmal',
            'fehlerort',
            'kostenstelle',
            'vorgang',
            'ursache'
        ])
        ->where('type', '=', 'normal');

        // Spezielle Behandlung für relationale Felder
        switch ($this->sortField) {
            case 'fehlermerkmal':
                $query->join('fehlermerkmal_normals', function($join) {
                    $join->on('ausschussbuchungen_sap.fehlermerkmal_id', '=', 'fehlermerkmal_normals.id')
                         ->where('ausschussbuchungen_sap.fehlermerkmal_type', '=', 'App\\Models\\FehlermermalNormal')
                         ->whereNull('fehlermerkmal_normals.deleted_at');
                })
                ->orderBy(DB::raw("CONCAT(fehlermerkmal_normals.sap_code, ' - ', fehlermerkmal_normals.abkuerzung, ' ', fehlermerkmal_normals.beschreibung)"), $this->sortDirection)
                ->select('ausschussbuchungen_sap.*');
                break;
            case 'kostenstelle':
                $query->join('kostenstellen', 'ausschussbuchungen_sap.kostenstelle_id', '=', 'kostenstellen.id')
                      ->orderBy('kostenstellen.kostenstelle', $this->sortDirection)
                      ->select('ausschussbuchungen_sap.*');
                break;
            case 'vorgang':
                $query->join('vorgangs', function($join) {
                    $join->on('ausschussbuchungen_sap.vorgang', '=', 'vorgangs.code');
                })
                ->orderBy(DB::raw("CONCAT(vorgangs.code, ' ', vorgangs.beschreibung)"), $this->sortDirection)
                ->select('ausschussbuchungen_sap.*');
                break;
            case 'ursache':
                $query->join('ursachen', function($join) {
                    $join->on('ausschussbuchungen_sap.ursache', '=', 'ursachen.code');
                })
                ->orderBy(DB::raw("CONCAT(ursachen.code, ' ', ursachen.beschreibung)"), $this->sortDirection)
                ->select('ausschussbuchungen_sap.*');
                break;
            case 'modellnest':
                $query->orderBy('modellnest', $this->sortDirection);
                break;
            case 'menge':
                $query->orderBy('menge', $this->sortDirection);
                break;
            default:
                $query->orderBy($this->sortField, $this->sortDirection);
        }

        return $query->get();
    }

    public function render()
    {
        return view('livewire.daten-normal-component', [
            'buchungen' => $this->buchungen
        ])->layout('layouts.app');
    }
} 