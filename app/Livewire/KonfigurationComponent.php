<?php

namespace App\Livewire;

use App\Models\FehlermermalNormal;
use App\Models\FehlermermalPutzen;
use App\Models\FehlermermalStrahlen;
use App\Models\Fehlerort;
use App\Models\Kostenstelle;
use App\Models\Ursache;
use App\Models\Vorgang;
use App\Models\Zuordnung;
use App\Models\AusschussbuchungSap;
use App\Models\VorlaeufigeBuchung;
use Livewire\Component;
use Livewire\WithPagination;
use Carbon\Carbon;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;

class KonfigurationComponent extends Component
{
    use WithPagination;

    public $activeTab = 'fehlermerkmal_normal';
    public $search = '';
    public $itemsPerPage = 10;
    public $isEditing = false;
    public $editingId;
    public $editModel = [
        'code' => '',
        'sap_code' => '',
        'abkuerzung' => '',
        'beschreibung' => '',
        'name' => '',
        'fehlerkey_sap' => '',
        'kostenstelle' => '',
        'bezeichnung' => '',
        'kategorie' => '',
        'zuordnungmodellnest' => '',
        'zuordnunglage' => '',
    ];
    public $sortField = 'id';
    public $sortDirection = 'asc';
    public $showConfirmModal = false;
    public $tableToTruncate = '';
    public $showImageDeleteModal = false;
    public $deleteOption = ''; // 'all' oder 'selected'
    public $selectedImages = [];
    public $deleteTimeframe = 'all'; // Neue Property für den Zeitrahmen
    public $customStartDate = null;
    public $customEndDate = null;

    protected $queryString = [
        'activeTab' => ['except' => 'fehlermerkmal_normal'],
        'search' => ['except' => ''],
        'page' => ['except' => 1],
    ];

    public function setActiveTab($tab)
    {
        $this->activeTab = $tab;
        $this->resetPage();
        $this->reset(['isEditing', 'editingId', 'editModel']);
    }

    public function edit($id)
    {
        $this->editingId = $id;
        $this->isEditing = true;

        $model = match($this->activeTab) {
            'fehlermerkmal_normal' => FehlermermalNormal::find($id),
            'fehlermerkmal_putzen' => FehlermermalPutzen::find($id),
            'fehlermerkmal_strahlen' => FehlermermalStrahlen::find($id),
            'fehlerort' => Fehlerort::find($id),
            'kostenstelle' => Kostenstelle::find($id),
            'ursache' => Ursache::find($id),
            'vorgang' => Vorgang::find($id),
            'zuordnung' => Zuordnung::find($id),
            default => null,
        };

        if (!$model) {
            $this->reset(['isEditing', 'editingId', 'editModel']);
            return;
        }

        $this->editModel = match($this->activeTab) {
            'fehlermerkmal_normal', 'fehlermerkmal_putzen', 'fehlermerkmal_strahlen' => [
                'code' => $model->code,
                'sap_code' => $model->sap_code,
                'abkuerzung' => $model->abkuerzung,
                'beschreibung' => $model->beschreibung,
            ],
            'fehlerort' => [
                'name' => $model->name,
                'fehlerkey_sap' => $model->fehlerkey_sap,
            ],
            'kostenstelle' => [
                'kostenstelle' => $model->kostenstelle,
                'bezeichnung' => $model->bezeichnung,
            ],
            'ursache' => [
                'code' => $model->code,
                'beschreibung' => $model->beschreibung,
                'kategorie' => $model->kategorie,
            ],
            'vorgang' => [
                'code' => $model->code,
                'beschreibung' => $model->beschreibung,
            ],
            'zuordnung' => [
                'zuordnungmodellnest' => $model->zuordnungmodellnest,
                'zuordnunglage' => $model->zuordnunglage,
            ],
            default => [],
        };
    }

    public function getDataQuery()
    {
        return match($this->activeTab) {
            'fehlermerkmal_normal' => FehlermermalNormal::query(),
            'fehlermerkmal_putzen' => FehlermermalPutzen::query(),
            'fehlermerkmal_strahlen' => FehlermermalStrahlen::query(),
            'fehlerort' => Fehlerort::query(),
            'kostenstelle' => Kostenstelle::query(),
            'ursache' => Ursache::query(),
            'vorgang' => Vorgang::query(),
            'zuordnung' => Zuordnung::query(),
            default => null,
        };
    }

    protected function getSearchColumns()
    {
        return match($this->activeTab) {
            'fehlermerkmal_normal', 'fehlermerkmal_putzen', 'fehlermerkmal_strahlen' => [
                'code', 'abkuerzung', 'beschreibung'
            ],
            'fehlerort' => [
                'name', 'fehlerkey_sap'
            ],
            'kostenstelle' => [
                'kostenstelle', 'bezeichnung'
            ],
            'ursache' => [
                'code', 'beschreibung', 'kategorie'
            ],
            'vorgang' => [
                'code', 'beschreibung'
            ],
            'zuordnung' => [
                'zuordnungmodellnest', 'zuordnunglage'
            ],
            default => ['id'],
        };
    }

    public function render()
    {
        $query = $this->getDataQuery();

        if ($query) {
            $data = $query
                ->when($this->search, function ($query) {
                    $query->where(function($q) {
                        $columns = $this->getSearchColumns();
                        foreach ($columns as $column) {
                            $q->orWhere($column, 'like', '%' . $this->search . '%');
                        }
                    });
                })
                ->orderBy($this->sortField, $this->sortDirection)
                ->paginate($this->itemsPerPage);
        } else {
            $data = collect();
        }

        return view('livewire.konfiguration-component', [
            'data' => $data
        ]);
    }

    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortField = $field;
            $this->sortDirection = 'asc';
        }
    }

    public function confirmTruncateTable($table)
    {
        $this->tableToTruncate = $table;
        $this->deleteTimeframe = 'all';
        $this->customStartDate = null;
        $this->customEndDate = null;
        $this->showConfirmModal = true;
    }

    public function truncateTable()
    {
        try {
            $tableName = match($this->tableToTruncate) {
                'ausschussbuchungen_sap' => 'ausschussbuchungen_sap',
                'vorlaeufige_ausschussbuchungen' => 'vorlaeufige_ausschussbuchungen',
                default => null,
            };

            if (!$tableName) {
                session()->flash('error', 'Ungültige Tabelle ausgewählt.');
                return;
            }

            // Für ausschussbuchungen_sap, zuerst die Bilder löschen
            if ($tableName === 'ausschussbuchungen_sap') {
                $buchungen = DB::table($tableName)
                    ->whereNotNull('foto_path')
                    ->get();

                foreach ($buchungen as $buchung) {
                    if ($buchung->foto_path) {
                        $fotoPaths = json_decode($buchung->foto_path, true);
                        if (is_array($fotoPaths)) {
                            foreach ($fotoPaths as $path) {
                                if (Storage::exists('public/'.$path)) {
                                    Storage::delete('public/'.$path);
                                }
                            }
                        }
                    }
                }
            }

            // Lösche alle Einträge aus der Tabelle
            $count = DB::table($tableName)->count();
            DB::table($tableName)->truncate();

            $displayTableName = match($tableName) {
                'ausschussbuchungen_sap' => 'Ausschussbuchungen SAP',
                'vorlaeufige_ausschussbuchungen' => 'Vorläufige Ausschussbuchungen',
                default => $tableName,
            };

            session()->flash('message', "Alle {$count} Einträge wurden aus der Tabelle {$displayTableName} gelöscht.");

            $this->showConfirmModal = false;
            $this->tableToTruncate = '';
            $this->deleteTimeframe = 'all';
            $this->customStartDate = null;
            $this->customEndDate = null;

        } catch (\Exception $e) {
            session()->flash('error', 'Fehler beim Löschen der Daten: ' . $e->getMessage());
            \Log::error('Fehler beim Löschen der Tabelle: ' . $e->getMessage());
        }
    }

    public function cancelTruncate()
    {
        $this->showConfirmModal = false;
        $this->tableToTruncate = '';
    }

    public function save()
    {
        $model = match($this->activeTab) {
            'fehlermerkmal_normal' => FehlermermalNormal::find($this->editingId),
            'fehlermerkmal_putzen' => FehlermermalPutzen::find($this->editingId),
            'fehlermerkmal_strahlen' => FehlermermalStrahlen::find($this->editingId),
            'fehlerort' => Fehlerort::find($this->editingId),
            'kostenstelle' => Kostenstelle::find($this->editingId),
            'ursache' => Ursache::find($this->editingId),
            'vorgang' => Vorgang::find($this->editingId),
            'zuordnung' => Zuordnung::find($this->editingId),
            default => null,
        };

        if (!$model) {
            session()->flash('error', 'Datensatz nicht gefunden.');
            return;
        }

        try {
            switch($this->activeTab) {
                case 'fehlermerkmal_normal':
                case 'fehlermerkmal_putzen':
                case 'fehlermerkmal_strahlen':
                    $model->code = $this->editModel['code'];
                    $model->sap_code = $this->editModel['sap_code'];
                    $model->abkuerzung = $this->editModel['abkuerzung'];
                    $model->beschreibung = $this->editModel['beschreibung'];
                    break;

                case 'fehlerort':
                    $model->name = $this->editModel['name'];
                    $model->fehlerkey_sap = $this->editModel['fehlerkey_sap'];
                    break;

                case 'kostenstelle':
                    $model->kostenstelle = $this->editModel['kostenstelle'];
                    $model->bezeichnung = $this->editModel['bezeichnung'];
                    break;

                case 'ursache':
                    $model->code = $this->editModel['code'];
                    $model->beschreibung = $this->editModel['beschreibung'];
                    $model->kategorie = $this->editModel['kategorie'];
                    break;

                case 'vorgang':
                    $model->code = $this->editModel['code'];
                    $model->beschreibung = $this->editModel['beschreibung'];
                    break;

                case 'zuordnung':
                    $model->zuordnungmodellnest = $this->editModel['zuordnungmodellnest'];
                    $model->zuordnunglage = $this->editModel['zuordnunglage'];
                    break;
            }

            $model->save();
            session()->flash('message', 'Änderungen wurden erfolgreich gespeichert.');
            $this->reset(['isEditing', 'editingId', 'editModel']);
        } catch (\Exception $e) {
            session()->flash('error', 'Fehler beim Speichern: ' . $e->getMessage());
        }
    }

    public function confirmImageDelete($option = 'all')
    {
        $this->deleteOption = $option;
        $this->deleteTimeframe = 'all';
        $this->customStartDate = null;
        $this->customEndDate = null;
        $this->showImageDeleteModal = true;
    }

    public function deleteImages()
    {
        try {
            $deletedFilesCount = 0;
            $deletedTmpFiles = 0;

            // Debug: Zeige Storage-Pfad
            \Log::info('Storage Path:', [
                'base_path' => storage_path(),
                'public_path' => storage_path('app/public'),
                'fotos_path' => storage_path('app/public/fotos'),
                'livewire_tmp' => storage_path('app/livewire-tmp')
            ]);

            // Lösche Dateien direkt aus den Verzeichnissen
            $directories = [
                '/var/www/vhosts/calhan.de/httpdocs/storage/app/public/fotos',
                '/var/www/vhosts/calhan.de/httpdocs/public/storage/fotos'
            ];

            foreach ($directories as $directory) {
                if (is_dir($directory)) {
                    $files = glob($directory . '/*');
                    \Log::info('Processing directory:', [
                        'directory' => $directory,
                        'files_found' => count($files)
                    ]);

                    foreach ($files as $file) {
                        if (is_file($file)) {
                            try {
                                unlink($file);
                                $deletedFilesCount++;
                                \Log::info('Successfully deleted file', ['path' => $file]);
                            } catch (\Exception $e) {
                                \Log::error('Failed to delete file', [
                                    'path' => $file,
                                    'error' => $e->getMessage()
                                ]);
                            }
                        }
                    }
                }
            }

            // Lösche Dateien im livewire-tmp Verzeichnis
            $tmpPaths = [
                storage_path('app/livewire-tmp'),
                '/var/www/vhosts/calhan.de/httpdocs/storage/app/livewire-tmp'
            ];

            foreach ($tmpPaths as $path) {
                if (is_dir($path)) {
                    $files = glob($path . '/*');
                    if ($files !== false) {
                        foreach ($files as $file) {
                            if (is_file($file) && is_writable($file)) {
                                if (unlink($file)) {
                                    $deletedTmpFiles++;
                                }
                            }
                        }
                    }
                }
            }

            // Setze alle foto_path Einträge in der Datenbank auf null
            DB::table('ausschussbuchungen_sap')
                ->whereNotNull('foto_path')
                ->update(['foto_path' => null]);

            $message = [];
            if ($deletedFilesCount > 0) {
                $message[] = "{$deletedFilesCount} Bilder wurden gelöscht";
            }
            if ($deletedTmpFiles > 0) {
                $message[] = "{$deletedTmpFiles} temporäre Dateien wurden gelöscht";
            }

            if (!empty($message)) {
                session()->flash('message', implode(' und ', $message) . '.');
            } else {
                session()->flash('message', "Keine Dateien zum Löschen gefunden.");
            }

        } catch (\Exception $e) {
            session()->flash('error', 'Fehler beim Löschen der Bilder: ' . $e->getMessage());
            \Log::error('Fehler beim Löschen der Bilder: ' . $e->getMessage());
        }

        $this->showImageDeleteModal = false;
        $this->deleteTimeframe = 'all';
        $this->customStartDate = null;
        $this->customEndDate = null;
    }

    public function cancelImageDelete()
    {
        $this->showImageDeleteModal = false;
        $this->deleteOption = '';
        $this->selectedImages = [];
    }
}
