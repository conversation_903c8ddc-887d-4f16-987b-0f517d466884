<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class VorlaeufigeBuchung extends Model
{
    use SoftDeletes;

    protected $table = 'vorlaeufige_ausschussbuchungen';

    protected $fillable = [
        'rueckmeldenummer',
        'losgroesse',
        'modellnest',
        'fehlermerkmal_id',
        'fehlermerkmal_type',
        'fehlerort_id',
        'menge',
        'kommentar',
        'foto_path',
        'type',
        'vorgang',
        'kostenstelle_id',
        'ursache',
    ];

    protected $casts = [
        'foto_path' => 'array',
        'losgroesse' => 'integer',
    ];

    public function fehlermerkmal()
    {
        return $this->morphTo();
    }

    public function fehlerort()
    {
        return $this->belongsTo(Fehlerort::class);
    }

    public function kostenstelle()
    {
        return $this->belongsTo(Kostenstelle::class);
    }

    public function toAusschussbuchung()
    {
        return [
            'rueckmeldenummer' => $this->rueckmeldenummer,
            'losgroesse' => $this->losgroesse,
            'modellnest' => $this->modellnest,
            'fehlermerkmal_type' => $this->fehlermerkmal_type,
            'fehlermerkmal_id' => $this->fehlermerkmal_id,
            'fehlerort_id' => $this->fehlerort_id,
            'menge' => $this->menge,
            'kommentar' => $this->kommentar,
            'foto_path' => $this->foto_path,
            'type' => $this->type,
            'vorgang' => $this->vorgang,
            'kostenstelle_id' => $this->kostenstelle_id,
            'ursache' => $this->ursache,
        ];
    }
}
