<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ExportSap extends Model
{
    use HasFactory;

    protected $table = 'ausschussbuchungen_sap'; // Verknüpfung mit der Tabelle in der Datenbank

    protected $fillable = [
        'rueckmeldenummer',
        'modellnest',
        'fehlermerkmal_id',
        'fehlermerkmal_type',
        'fehlerort_id',
        'menge',
        'kommentar',
        'foto_path',
        'type',
    ];

    // Beziehung zur fehlerort-Tabelle
    public function fehlerort()
    {
        return $this->belongsTo(Fehlerort::class, 'fehlerort_id');
    }

    // Polymorphe <PERSON>g zu fehlermerkmal_putzens und fehlermerkmal_strahlens
    public function fehlermerkmal()
    {
        return $this->morphTo(__FUNCTION__, 'fehlermerkmal_type', 'fehlermerkmal_id');
    }
}
