<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class FehlermermalStrahlen extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'fehlermerkmal_strahlens';

    protected $fillable = [
        'code',
        'abkuerzung',
        'sap_code',
        'beschreibung',
    ];

    public function ausschussbuchungen()
    {
        return $this->morphMany(AusschussbuchungSap::class, 'fehlermerkmal');
    }
}
