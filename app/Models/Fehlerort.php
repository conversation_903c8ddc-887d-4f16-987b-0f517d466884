<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Fehlerort extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'fehlerorts';

    protected $fillable = [
        'name',
        'kostenstelle_id',
        'fehlerkey_sap',
    ];

    public function ausschussbuchungen()
    {
        return $this->hasMany(AusschussbuchungSap::class);
    }

    public function kostenstelle()
    {
        return $this->belongsTo(Kostenstelle::class);
    }
}
