<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Kostenstelle extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'kostenstellen';

    protected $fillable = [
        'kostenstelle',
        'bezeichnung',
        'hierarchie_bereich',
        'verantwortlicher',
    ];

    // Beziehung zu Fehlerort
    public function fehlerorte()
    {
        return $this->hasMany(Fehlerort::class, 'kostenstelle_id');
    }
}
