<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AusschussbuchungSap extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'ausschussbuchungen_sap';

    protected $fillable = [
        'rueckmeldenummer',
        'losgroesse',
        'modellnest',
        'fehlermerkmal_id',
        'fehlermerkmal_type',
        'fehlerort_id',
        'menge',
        'kommentar',
        'foto_path',
        'type',
        'vorgang',
        'kostenstelle_id',
        'ursache'
    ];

    protected $casts = [
        'foto_path' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
        'losgroesse' => 'integer',
    ];

    public function fehlermerkmal()
    {
        return $this->morphTo();
    }

    public function fehlerort()
    {
        return $this->belongsTo(Fehlerort::class);
    }

    public function kostenstelle()
    {
        return $this->belongsTo(Kostenstelle::class, 'kostenstelle_id');
    }

    public function vorgang()
    {
        return $this->belongsTo(Vorgang::class, 'vorgang', 'code');
    }

    public function ursache()
    {
        return $this->belongsTo(Ursache::class, 'ursache', 'code');
    }

    public function getFotoPathAttribute($value)
    {
        if (empty($value)) {
            return [];
        }
        
        if (is_string($value)) {
            $decoded = json_decode($value, true);
            return is_array($decoded) ? $decoded : [];
        }
        
        return is_array($value) ? $value : [];
    }

    public function setFotoPathAttribute($value)
    {
        if (is_string($value)) {
            $decoded = json_decode($value, true);
            $this->attributes['foto_path'] = json_encode(is_array($decoded) ? $decoded : []);
        } else {
            $this->attributes['foto_path'] = json_encode(is_array($value) ? $value : []);
        }
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (! $model->type) {
                $model->type = 'strahlen';
            }
        });
    }
}
