<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class CleanupLivewireTmp extends Command
{
    protected $signature = 'livewire:cleanup-tmp';
    protected $description = 'Clean up temporary Livewire files';

    public function handle()
    {
        $path = '/var/www/vhosts/calhan.de/httpdocs/storage/app/public/livewire-tmp';
        $path = '/var/www/vhosts/calhan.de/httpdocs/storage/app/private/livewire-tmp';
        if (!is_dir($path)) {
            mkdir($path, 0775, true);
            $this->info("Created directory: {$path}");
        }

        $files = glob($path . '/*');
        $count = 0;

        foreach ($files as $file) {
            if (is_file($file)) {
                unlink($file);
                $count++;
            }
        }

        $this->info("Successfully deleted {$count} temporary files.");
        
        // Set correct permissions
        chmod($path, 0775);
        chown($path, 'calhan.de');
        chgrp($path, 'psacln');
        
        return 0;
    }
} 