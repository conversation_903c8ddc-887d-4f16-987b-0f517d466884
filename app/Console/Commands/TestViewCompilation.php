<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\View;

class TestViewCompilation extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:views';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test view compilation';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $this->info('Testing view compilation...');

            // Test simple view compilation
            $content = View::make('layouts.guest', ['slot' => 'Test content'])->render();
            $this->info('Guest layout compiled successfully');

            // Check if views directory has files now
            $viewsPath = storage_path('framework/views');
            $files = glob($viewsPath . '/*.php');
            $this->info('Compiled view files: ' . count($files));

            foreach ($files as $file) {
                $this->info('- ' . basename($file));
            }

            $this->info('View compilation test completed successfully!');

        } catch (\Exception $e) {
            $this->error('View compilation failed: ' . $e->getMessage());
            return 1;
        }

        return 0;
    }
}
