<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    public function index()
    {
        $buchungen = DB::table('ausschussbuchungen_sap AS sap')
            ->leftJoin('fehlerorts AS fo', 'sap.fehlerort_id', '=', 'fo.id')
            ->leftJoin('fehlermerkmal_putzens AS fp', function ($join) {
                $join->on('sap.fehlermerkmal_id', '=', 'fp.id')
                    ->where('sap.fehlermerkmal_type', '=', 'App\\Models\\FehlermermalPutzen');
            })
            ->leftJoin('fehlermerkmal_strahlens AS fs', function ($join) {
                $join->on('sap.fehlermerkmal_id', '=', 'fs.id')
                    ->where('sap.fehlermerkmal_type', '=', 'App\\Models\\FehlermermalStrahlen');
            })
            ->select(
                'sap.rueckmeldenummer',
                'sap.type',
                'sap.modellnest',
                'sap.created_at',
                'fo.fehlerkey_sap',
                'fo.name AS fehlerort_name',
                DB::raw('COALESCE(fp.beschreibung, fs.beschreibung) AS fehlermerkmal_beschreibung'),
                DB::raw('COALESCE(fp.code, fs.code) AS fehlermerkmal_code'),
                DB::raw('COALESCE(fp.abkuerzung, fs.abkuerzung) AS fehlermerkmal_abkuerzung'),
                'sap.menge as gesamt_menge'
            )
            ->whereNull('sap.deleted_at')
            ->orderBy('sap.created_at', 'desc')
            ->limit(10)
            ->get();

        return view('dashboard', compact('buchungen'));
    }
}
