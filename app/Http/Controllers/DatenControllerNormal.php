<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\DB;
use App\Models\AusschussbuchungSap;
use App\Models\Vorgang;
use App\Models\Ursache;
use App\Models\Kostenstelle;
use App\Models\FehlermermalNormal;

class DatenControllerNormal extends Controller
{
    public function index()
    {
        $buchungen = AusschussbuchungSap::with([
            'fehlermerkmal',
            'fehlerort',
            'kostenstelle',
            'vorgang',
            'ursache'
        ])
        ->where('type', '=', 'normal')
        ->orderBy('rueckmeldenummer', 'desc')
        ->get();

        // Lade die zugehörigen Vorgänge und Ursachen
        $vorgangCodes = $buchungen->pluck('vorgang')->unique()->filter();
        $ursacheCodes = $buchungen->pluck('ursache')->unique()->filter();

        $vorgaenge = Vorgang::whereIn('code', $vorgangCodes)->get()->keyBy('code');
        $ursachen = Ursache::whereIn('code', $ursacheCodes)->get()->keyBy('code');

        // Füge die Beschreibungen zu den Buchungen hinzu
        foreach ($buchungen as $buchung) {
            $buchung->vorgang_beschreibung = $vorgaenge->get($buchung->vorgang)?->beschreibung ?? '';
            $buchung->ursache_beschreibung = $ursachen->get($buchung->ursache)?->beschreibung ?? '';
        }

        // Vorbereitung der Chart-Daten
        $chartData = [
            'modellnest' => [
                'labels' => [],
                'values' => []
            ],
            'vorgang' => [
                'labels' => [],
                'values' => []
            ],
            'kostenstelle' => [
                'labels' => [],
                'values' => []
            ],
            'fehlermerkmal' => [
                'labels' => [],
                'values' => []
            ]
        ];

        // Modellnest Daten
        $modellnestData = $buchungen->groupBy('modellnest')
            ->map(fn($group) => $group->sum('menge'))
            ->sortDesc();
        $chartData['modellnest']['labels'] = $modellnestData->keys()->toArray();
        $chartData['modellnest']['values'] = $modellnestData->values()->toArray();

        // Vorgang Daten
        $vorgangData = $buchungen->groupBy('vorgang')
            ->map(function($group) use ($vorgaenge) {
                $code = $group->first()->vorgang;
                $beschreibung = $vorgaenge->get($code)?->beschreibung ?? '';
                return [
                    'label' => $code . ($beschreibung ? ' - ' . $beschreibung : ''),
                    'value' => $group->sum('menge')
                ];
            })
            ->sortByDesc('value');
        $chartData['vorgang']['labels'] = $vorgangData->pluck('label')->toArray();
        $chartData['vorgang']['values'] = $vorgangData->pluck('value')->toArray();

        // Kostenstelle Daten
        $kostenstelleData = $buchungen->groupBy('kostenstelle.kostenstelle')
            ->map(function($group) {
                $kostenstelle = $group->first()->kostenstelle;
                return [
                    'label' => $kostenstelle->kostenstelle . ' - ' . $kostenstelle->bezeichnung,
                    'value' => $group->sum('menge')
                ];
            })
            ->sortByDesc('value');
        $chartData['kostenstelle']['labels'] = $kostenstelleData->pluck('label')->toArray();
        $chartData['kostenstelle']['values'] = $kostenstelleData->pluck('value')->toArray();

        // Fehlermerkmal Daten
        $fehlermerkmalData = $buchungen->groupBy('fehlermerkmal.sap_code')
            ->map(function($group) {
                $fehlermerkmal = $group->first()->fehlermerkmal;
                return [
                    'label' => $fehlermerkmal->sap_code . ' - ' . $fehlermerkmal->beschreibung,
                    'value' => $group->sum('menge')
                ];
            })
            ->sortByDesc('value');
        $chartData['fehlermerkmal']['labels'] = $fehlermerkmalData->pluck('label')->toArray();
        $chartData['fehlermerkmal']['values'] = $fehlermerkmalData->pluck('value')->toArray();

        // Debug logging
        \Log::info('Chart data prepared:', $chartData);

        return view('daten.normal.uebersicht', compact('buchungen', 'chartData'));
    }

    public function exportCSV()
    {
        $buchungen = DB::table('ausschussbuchungen_sap as a')
            ->select([
                'a.rueckmeldenummer',
                'a.modellnest',
                'a.vorgang',
                'k.kostenstelle',
                'k.bezeichnung as kostenstelle_bezeichnung',
                'm.code as fehlermerkmal_code',
                'm.sap_code as fehlermerkmal_sap_code',
                'm.abkuerzung as fehlermerkmal_abkuerzung',
                'm.beschreibung as fehlermerkmal_beschreibung',
                'a.ursache',
                DB::raw('SUM(COALESCE(a.menge, 0)) as menge'),
            ])
            ->join('kostenstellen as k', 'a.kostenstelle_id', '=', 'k.id')
            ->join(DB::raw('(
                SELECT id, code, sap_code, abkuerzung, beschreibung FROM fehlermerkmal_normals
            ) as m'), function ($join) {
                $join->on('a.fehlermerkmal_id', '=', 'm.id')
                    ->where('a.fehlermerkmal_type', '=', 'App\\Models\\FehlermermalNormal');
            })
            ->where('a.type', '=', 'normal')
            ->whereNull('a.deleted_at')
            ->groupBy(
                'a.rueckmeldenummer',
                'a.modellnest',
                'a.vorgang',
                'k.kostenstelle',
                'k.bezeichnung',
                'm.code',
                'm.sap_code',
                'm.abkuerzung',
                'm.beschreibung',
                'a.ursache'
            )
            ->orderBy('a.rueckmeldenummer')
            ->get();

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="ausschussbuchungen_normal.csv"',
            'Pragma' => 'no-cache',
            'Cache-Control' => 'must-revalidate, post-check=0, pre-check=0',
            'Expires' => '0',
        ];

        $callback = function () use ($buchungen) {
            $file = fopen('php://output', 'w');
            fputcsv($file, [
                'Rückmeldenummer',
                'Code',
                'Menge',
            ]);

            foreach ($buchungen as $buchung) {
                // First line: CODE-SAP_CODE
                fputcsv($file, [
                    $buchung->rueckmeldenummer,
                    $buchung->fehlermerkmal_code . '-' . $buchung->fehlermerkmal_sap_code,
                    $buchung->menge
                ]);

                // Second line: CODE-1000-MODELLNEST
                fputcsv($file, [
                    $buchung->rueckmeldenummer,
                    $buchung->fehlermerkmal_code . '-1000-' . ($buchung->modellnest ?: 'OHNE'),
                    $buchung->menge
                ]);

                // Third line: CODE-2000-FEHLERKEY
                fputcsv($file, [
                    $buchung->rueckmeldenummer,
                    $buchung->fehlermerkmal_code . '-2000-00',
                    $buchung->menge
                ]);
            }
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    public function exportJSON()
    {
        $buchungen = DB::table('ausschussbuchungen_sap as a')
            ->select([
                'a.rueckmeldenummer',
                'a.vorgang',
                'k.kostenstelle',
                'm.sap_code as fehlermerkmal_sap_code',
                'a.ursache',
                DB::raw('SUM(COALESCE(a.menge, 0)) as menge'),
            ])
            ->join('kostenstellen as k', 'a.kostenstelle_id', '=', 'k.id')
            ->join(DB::raw('(
                SELECT id, sap_code FROM fehlermerkmal_normals
            ) as m'), function ($join) {
                $join->on('a.fehlermerkmal_id', '=', 'm.id')
                    ->where('a.fehlermerkmal_type', '=', 'App\\Models\\FehlermermalNormal');
            })
            ->where('a.type', '=', 'normal')
            ->whereNull('a.deleted_at')
            ->groupBy(
                'a.rueckmeldenummer',
                'a.vorgang',
                'k.kostenstelle',
                'm.sap_code',
                'a.ursache'
            )
            ->orderBy('a.rueckmeldenummer')
            ->get()
            ->map(function ($buchung) {
                return [
                    $buchung->rueckmeldenummer,
                    $buchung->vorgang,
                    $buchung->kostenstelle,
                    $buchung->fehlermerkmal_sap_code,
                    $buchung->ursache,
                    $buchung->menge
                ];
            });

        return response()->json($buchungen)
            ->header('Content-Type', 'application/json')
            ->header('Access-Control-Allow-Origin', '*')
            ->header('Cache-Control', 'no-cache, no-store, must-revalidate');
    }
}
