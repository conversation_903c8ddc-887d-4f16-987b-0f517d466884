<?php
// Es ist das Controller für die Datenübersicht CSV und JSON Export
namespace App\Http\Controllers;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class DatenControllerExportSap extends Controller
{
    private function formatModellnest($modellnest)
    {
        if (preg_match('/^M(\d+)$/', $modellnest, $matches)) {
            return str_pad($matches[1], 2, '0', STR_PAD_LEFT);
        }
        return $modellnest;
    }

    public function index()
    {
        $buchungen = DB::table('ausschussbuchungen_sap as a')
            ->select([
                'a.rueckmeldenummer',
                'a.type',
                'a.modellnest',
                'f.fehlerkey_sap',
                'f.name as fehlerort_name',
                'm.code as fehlermerkmal_code',
                'm.sap_code as fehlermerkmal_sap_code',
                'm.abkuerzung as fehlermerkmal_abkuerzung',
                'm.beschreibung as fehlermerkmal_beschreibung',
                DB::raw('SUM(COALESCE(a.menge, 0)) as menge'),
            ])
            ->join('fehlerorts as f', 'a.fehlerort_id', '=', 'f.id')
            ->join(DB::raw('(
                SELECT id, code, sap_code, abkuerzung, beschreibung, "strahlen" as type FROM fehlermerkmal_strahlens
                UNION ALL
                SELECT id, code, sap_code, abkuerzung, beschreibung, "putzen" as type FROM fehlermerkmal_putzens
            ) as m'), function ($join) {
                $join->on('a.fehlermerkmal_id', '=', 'm.id')
                    ->whereRaw('a.type = m.type');
            })
            ->whereNull('a.deleted_at')
            ->groupBy(
                'a.rueckmeldenummer',
                'a.type',
                'a.modellnest',
                'f.fehlerkey_sap',
                'f.name',
                'm.code',
                'm.sap_code',
                'm.abkuerzung',
                'm.beschreibung'
            )
            ->orderBy('a.rueckmeldenummer')
            ->orderBy('a.modellnest')
            ->get()
            ->map(function ($buchung) {
                $buchung->modellnest = $this->formatModellnest($buchung->modellnest);
                return $buchung;
            });

        return view('daten.uebersicht', compact('buchungen'));
    }

    public function exportCSV()
    {
        $buchungen = DB::table('ausschussbuchungen_sap AS sap')
            ->leftJoin('fehlerorts AS fo', 'sap.fehlerort_id', '=', 'fo.id')
            ->leftJoin('fehlermerkmal_putzens AS fp', function ($join) {
                $join->on('sap.fehlermerkmal_id', '=', 'fp.id')
                    ->where('sap.fehlermerkmal_type', '=', 'App\\Models\\FehlermermalPutzen');
            })
            ->leftJoin('fehlermerkmal_strahlens AS fs', function ($join) {
                $join->on('sap.fehlermerkmal_id', '=', 'fs.id')
                    ->where('sap.fehlermerkmal_type', '=', 'App\\Models\\FehlermermalStrahlen');
            })
            ->leftJoin('zuordnungen AS z', function ($join) {
                $join->whereRaw('1=1'); // We'll use the values later
            })
            ->select([
                'sap.rueckmeldenummer',
                'sap.type',
                'sap.modellnest',
                'fo.fehlerkey_sap',
                'fo.name AS fehlerort_name',
                DB::raw('COALESCE(fp.beschreibung, fs.beschreibung) AS fehlermerkmal_beschreibung'),
                DB::raw('COALESCE(fp.code, fs.code) AS fehlermerkmal_code'),
                DB::raw('COALESCE(fp.sap_code, fs.sap_code) AS fehlermerkmal_sap_code'),
                DB::raw('COALESCE(fp.abkuerzung, fs.abkuerzung) AS fehlermerkmal_abkuerzung'),
                'z.zuordnungmodellnest',
                'z.zuordnunglage',
                DB::raw('SUM(COALESCE(sap.menge, 0)) AS gesamt_menge'),
            ])
            ->whereNull('sap.deleted_at')
            ->where('sap.type', '!=', 'normal')
            ->groupBy(
                'sap.rueckmeldenummer',
                'sap.type',
                'sap.modellnest',
                'fo.fehlerkey_sap',
                'fo.name',
                'fehlermerkmal_beschreibung',
                'fehlermerkmal_code',
                'fehlermerkmal_sap_code',
                'fehlermerkmal_abkuerzung',
                'z.zuordnungmodellnest',
                'z.zuordnunglage'
            )
            ->orderBy('fehlermerkmal_code', 'asc')
            ->orderBy('fehlermerkmal_abkuerzung', 'asc')
            ->orderBy('fehlermerkmal_beschreibung', 'asc')
            ->get()
            ->map(function ($buchung) {
                $buchung->modellnest = $this->formatModellnest($buchung->modellnest);
                return $buchung;
            });

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="ausschussbuchungen.csv"',
            'Pragma' => 'no-cache',
            'Cache-Control' => 'must-revalidate, post-check=0, pre-check=0',
            'Expires' => '0',
        ];

        $callback = function () use ($buchungen) {
            $file = fopen('php://output', 'w');
            fputcsv($file, [
                'Rückmeldenummer',
                'Code',
                'Menge'
            ]);

            foreach ($buchungen as $buchung) {
                // First line: CODE-SAP_CODE
                fputcsv($file, [
                    $buchung->rueckmeldenummer,
                    $buchung->fehlermerkmal_code . '-' . $buchung->fehlermerkmal_sap_code,
                    $buchung->gesamt_menge
                ]);

                // Second line: CODE-1000-MODELLNEST
                fputcsv($file, [
                    $buchung->rueckmeldenummer,
                    $buchung->fehlermerkmal_code . '-1000-' . $buchung->modellnest,
                    $buchung->gesamt_menge
                ]);

                // Third line: CODE-2000-FEHLERKEY
                fputcsv($file, [
                    $buchung->rueckmeldenummer,
                    $buchung->fehlermerkmal_code . '-2000-' . $buchung->fehlerkey_sap,
                    $buchung->gesamt_menge
                ]);
            }
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    public function exportJSON()
    {
        $buchungen = DB::table('ausschussbuchungen_sap AS sap')
            ->leftJoin('fehlerorts AS fo', 'sap.fehlerort_id', '=', 'fo.id')
            ->leftJoin('fehlermerkmal_putzens AS fp', function ($join) {
                $join->on('sap.fehlermerkmal_id', '=', 'fp.id')
                    ->where('sap.fehlermerkmal_type', '=', 'App\\Models\\FehlermermalPutzen');
            })
            ->leftJoin('fehlermerkmal_strahlens AS fs', function ($join) {
                $join->on('sap.fehlermerkmal_id', '=', 'fs.id')
                    ->where('sap.fehlermerkmal_type', '=', 'App\\Models\\FehlermermalStrahlen');
            })
            ->leftJoin('zuordnungen AS z', function ($join) {
                $join->whereRaw('1=1'); // We'll use the values later
            })
            ->select([
                'sap.rueckmeldenummer',
                'sap.type',
                'sap.modellnest',
                'fo.fehlerkey_sap',
                'fo.name AS fehlerort_name',
                DB::raw('COALESCE(fp.beschreibung, fs.beschreibung) AS fehlermerkmal_beschreibung'),
                DB::raw('COALESCE(fp.code, fs.code) AS fehlermerkmal_code'),
                DB::raw('COALESCE(fp.sap_code, fs.sap_code) AS fehlermerkmal_sap_code'),
                DB::raw('COALESCE(fp.abkuerzung, fs.abkuerzung) AS fehlermerkmal_abkuerzung'),
                'z.zuordnungmodellnest',
                'z.zuordnunglage',
                DB::raw('SUM(COALESCE(sap.menge, 0)) AS gesamt_menge'),
            ])
            ->whereNull('sap.deleted_at')
            ->where('sap.type', '!=', 'normal')
            ->groupBy(
                'sap.rueckmeldenummer',
                'sap.type',
                'sap.modellnest',
                'fo.fehlerkey_sap',
                'fo.name',
                'fehlermerkmal_beschreibung',
                'fehlermerkmal_code',
                'fehlermerkmal_sap_code',
                'fehlermerkmal_abkuerzung',
                'z.zuordnungmodellnest',
                'z.zuordnunglage'
            )
            ->orderBy('fehlermerkmal_code', 'asc')
            ->orderBy('fehlermerkmal_abkuerzung', 'asc')
            ->orderBy('fehlermerkmal_beschreibung', 'asc')
            ->get()
            ->map(function ($buchung) {
                $buchung->modellnest = $this->formatModellnest($buchung->modellnest);

                return [
                    $buchung->rueckmeldenummer,
                    $buchung->fehlermerkmal_code . '-' . $buchung->fehlermerkmal_sap_code,
                    $buchung->fehlermerkmal_code . '-1000-' . $buchung->modellnest,
                    $buchung->fehlermerkmal_code . '-2000-' . $buchung->fehlerkey_sap,
                    $buchung->gesamt_menge
                ];
            });

        return response()->json($buchungen)
            ->header('Content-Type', 'application/json')
            ->header('Access-Control-Allow-Origin', '*')
            ->header('Cache-Control', 'no-cache, no-store, must-revalidate');
    }
}
